import { create } from 'zustand';
import socketService from '../services/socketService';
import { apiService } from '../services/api';

// Helper function to wait for authentication to be ready
const waitForAuthentication = async (maxWaitMs: number = 2000): Promise<void> => {
  const startTime = Date.now();

  while (Date.now() - startTime < maxWaitMs) {
    const token = apiService.getToken();
    if (token) {
      console.log('Authentication ready, token available');
      return;
    }

    // Wait 50ms before checking again
    await new Promise(resolve => setTimeout(resolve, 50));
  }

  console.warn('Authentication wait timeout - proceeding without token');
};

export type Player = {
  id: string;
  name: string;
  avatar: string;
  isReady?: boolean;
  isHost?: boolean;
  team?: number; // 1 or 2
};

export type LobbyState = {
  lobbyCode: string | null;
  partnerInviteCode: string | null; // New code for inviting a partner (Team 1)
  opponentInviteCode: string | null; // New code for inviting opponents (Team 2)
  players: Player[];
  playerName: string;
  teamName: string;
  isHost: boolean;
  isConnected: boolean;
  isGameStarted: boolean;
  teamReady: {
    1: boolean;
    2: boolean;
  };
  teamNames: {
    1: string;
    2: string;
  };
  isFindingMatch: boolean;
  matchedLobby: string | null;
  matchedTeam: Player[] | null;
  matchedTeamName: string | null;
  error: string | null;
};

export type LobbyActions = {
  setPlayerName: (name: string) => void;
  setTeamName: (name: string) => void;
  updateTeamName: (teamNumber: 1 | 2, name: string) => Promise<void>;
  createLobby: () => Promise<string>;
  joinLobby: (code: string) => Promise<void>;
  startGame: () => Promise<void>;
  leaveLobby: () => void;
  updatePlayers: (players: Player[]) => void;
  setTeamReady: (ready: boolean) => Promise<void>;
  switchTeam: () => Promise<void>;
  findMatch: () => Promise<void>;
  cancelFindMatch: () => Promise<void>;
  setError: (error: string | null) => void;
  setGameStarted: (started: boolean) => void;
  setLobbyCode: (code: string) => void; // Added for spectator mode
  initializeCompetitionLobby: (lobbyData: {
    lobbyCode: string;
    partnerInviteCode: string;
    opponentInviteCode: string;
    teamName: string;
    playerName: string;
  }) => void;
  tryRejoinPreviousLobby: () => Promise<boolean>;
  hasActiveGameSession: () => boolean;
  reset: () => void;
};

// Initial state
const initialState: LobbyState = {
  lobbyCode: null,
  partnerInviteCode: null,
  opponentInviteCode: null,
  players: [],
  playerName: '',
  teamName: '',
  isHost: false,
  isConnected: false,
  isGameStarted: false,
  teamReady: {
    1: false,
    2: false
  },
  teamNames: {
    1: 'Team 1',
    2: 'Team 2'
  },
  isFindingMatch: false,
  matchedLobby: null,
  matchedTeam: null,
  matchedTeamName: null,
  error: null,
};

// Create the store
export const useLobbyStore = create<LobbyState & LobbyActions>((set, get) => ({
  ...initialState,

  setPlayerName: (name) => set({ playerName: name }),

  setTeamName: (name) => set({ teamName: name }),

  updateTeamName: async (teamNumber, name) => {
    const { lobbyCode, players } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    // Find current player to check their team
    const currentPlayerId = socketService.getSocketId();
    const currentPlayer = players.find(p => p.id === currentPlayerId);

    if (!currentPlayer) {
      set({ error: 'Player not found in lobby' });
      return Promise.reject('Player not found in lobby');
    }

    // Allow players to update their own team name only
    if (currentPlayer.team !== teamNumber) {
      set({ error: 'You can only update your own team name' });
      return Promise.reject('You can only update your own team name');
    }

    try {
      await socketService.updateTeamName(lobbyCode, teamNumber, name);

      // Update local state (will be overwritten by server event, but this makes the UI more responsive)
      set(state => ({
        teamNames: {
          ...state.teamNames,
          [teamNumber]: name
        }
      }));

      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to update team name' });
      return Promise.reject(error);
    }
  },

  createLobby: async () => {
    const { playerName, teamName } = get();

    if (!playerName) {
      set({ error: 'Please enter a player name' });
      return Promise.reject('Please enter a player name');
    }

    try {
      // Ensure authentication is ready before connecting
      await waitForAuthentication();

      // Connect to the socket server
      await socketService.connect(playerName);

      // Create a new lobby with team name via socket (this will also save to ThuneeAPI)
      const response = await socketService.createLobby(playerName, teamName);
      const { lobbyCode, partnerInviteCode, opponentInviteCode } = response;

      // Set up event listeners
      setupEventListeners(set, get);

      // Update state
      set({
        lobbyCode,
        partnerInviteCode,
        opponentInviteCode,
        isHost: true,
        isConnected: true,
        players: [{
          id: socketService.getSocketId() || 'host',
          name: playerName,
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${playerName}`,
          isHost: true,
          team: 1
        }],
        teamNames: {
          1: teamName || 'Team 1',
          2: 'Team 2'
        },
        error: null
      });

      // Save lobby info to localStorage for rejoin functionality
      const lobbyInfo = {
        lobbyCode,
        playerName,
        isHost: true,
        timestamp: Date.now()
      };
      localStorage.setItem('thunee_lobby_info', JSON.stringify(lobbyInfo));
      console.log('Saved lobby info to localStorage:', lobbyInfo);

      return lobbyCode;
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to create lobby' });
      return Promise.reject(error);
    }
  },

  joinLobby: async (code) => {
    const { playerName } = get();

    if (!playerName) {
      set({ error: 'Please enter a player name' });
      return Promise.reject('Please enter a player name');
    }

    try {
      // Ensure authentication is ready before connecting
      await waitForAuthentication();

      // Connect to the socket server
      await socketService.connect(playerName);

      // Join the lobby via socket (this handles both lobby codes and invite codes)
      const response = await socketService.joinLobby(code, playerName);

      // Set up event listeners
      setupEventListeners(set, get);

      // Update state
      const actualLobbyCode = response.actualLobbyCode || code;
      set({
        lobbyCode: actualLobbyCode,
        isHost: false,
        isConnected: true,
        error: null
      });

      // Save lobby info to localStorage for rejoin functionality
      const lobbyInfo = {
        lobbyCode: actualLobbyCode,
        playerName,
        isHost: false,
        timestamp: Date.now()
      };
      localStorage.setItem('thunee_lobby_info', JSON.stringify(lobbyInfo));
      console.log('Saved lobby info to localStorage:', lobbyInfo);

      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to join lobby' });
      return Promise.reject(error);
    }
  },

  startGame: async () => {
    const { lobbyCode, isHost } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    if (!isHost) {
      set({ error: 'Only the host can start the game' });
      return Promise.reject('Only the host can start the game');
    }

    try {
      await socketService.startGame(lobbyCode);
      set({ isGameStarted: true, error: null });
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to start game' });
      return Promise.reject(error);
    }
  },

  leaveLobby: () => {
    socketService.disconnect();
    set(initialState);
  },

  updatePlayers: (players) => set({ players }),

  setTeamReady: async (ready) => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      await socketService.setTeamReady(lobbyCode, ready);
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to set team ready status' });
      return Promise.reject(error);
    }
  },

  switchTeam: async () => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      await socketService.switchTeam(lobbyCode);
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to switch team' });
      return Promise.reject(error);
    }
  },

  findMatch: async () => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      set({ isFindingMatch: true, error: null });
      await socketService.findMatch(lobbyCode);
      return Promise.resolve();
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to find match',
        isFindingMatch: false
      });
      return Promise.reject(error);
    }
  },

  cancelFindMatch: async () => {
    const { lobbyCode } = get();

    if (!lobbyCode) {
      set({ error: 'No active lobby' });
      return Promise.reject('No active lobby');
    }

    try {
      set({ isFindingMatch: false, error: null });
      await socketService.cancelFindMatch(lobbyCode);
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to cancel match finding' });
      return Promise.reject(error);
    }
  },

  setError: (error) => set({ error }),

  setGameStarted: (started) => set({ isGameStarted: started }),

  setLobbyCode: (code) => set({ lobbyCode: code }),

  initializeCompetitionLobby: (lobbyData) => {
    // Set up event listeners first
    setupEventListeners(set, get);

    // Initialize the lobby state with competition data
    set({
      lobbyCode: lobbyData.lobbyCode,
      partnerInviteCode: lobbyData.partnerInviteCode,
      opponentInviteCode: lobbyData.opponentInviteCode,
      isHost: true,
      isConnected: true,
      playerName: lobbyData.playerName,
      players: [{
        id: socketService.getSocketId() || 'host',
        name: lobbyData.playerName,
        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${lobbyData.playerName}`,
        isHost: true,
        team: 1
      }],
      teamNames: {
        1: lobbyData.teamName,
        2: 'Team 2'
      },
      error: null
    });
  },

  tryRejoinPreviousLobby: async () => {
    const lobbyInfo = localStorage.getItem('thunee_lobby_info');
    if (!lobbyInfo) {
      console.log('No previous lobby info found');
      return false;
    }

    try {
      const parsedInfo = JSON.parse(lobbyInfo);
      const { lobbyCode, playerName, timestamp } = parsedInfo;

      // Check if the lobby info is not too old (e.g., within 2 hours)
      const twoHours = 2 * 60 * 60 * 1000;
      if (Date.now() - timestamp > twoHours) {
        console.log('Previous lobby info is too old');
        localStorage.removeItem('thunee_lobby_info');
        return false;
      }

      console.log(`Attempting to rejoin lobby ${lobbyCode} as ${playerName}`);

      // Ensure authentication is ready before connecting
      await waitForAuthentication();

      // Connect to the socket server first
      await socketService.connect(playerName);

      // First try to rejoin using the disconnection handler
      try {
        const persistentId = socketService.getPersistentPlayerId();
        if (persistentId) {
          console.log('Attempting rejoin with persistent ID:', persistentId);
          const rejoinResult = await socketService.sendCustomEvent('rejoinRequest', {
            persistentId,
            gameId: lobbyCode
          });

          if (rejoinResult.success) {
            console.log('Rejoin successful via persistent ID');

            // Set up event listeners
            setupEventListeners(set, get);

            // Update store state
            set({
              lobbyCode: rejoinResult.lobbyCode || lobbyCode,
              playerName,
              isHost: parsedInfo.isHost || false,
              isConnected: true,
              error: null
            });

            // Update localStorage with fresh timestamp after successful rejoin
            const updatedLobbyInfo = {
              lobbyCode: rejoinResult.lobbyCode || lobbyCode,
              playerName,
              isHost: parsedInfo.isHost || false,
              timestamp: Date.now()
            };
            localStorage.setItem('thunee_lobby_info', JSON.stringify(updatedLobbyInfo));
            console.log('Updated lobby info after successful rejoin via persistent ID:', updatedLobbyInfo);

            return true;
          } else {
            console.log('Rejoin failed, trying direct lobby join:', rejoinResult.error);
            // If rejoin failed because no disconnection record found, the player might already be connected
            if (rejoinResult.error && rejoinResult.error.includes('No disconnection record found')) {
              console.log('No disconnection record found - player might already be reconnected, trying direct join');
              // Continue to try direct lobby join to verify connection status
            }
          }
        }
      } catch (error) {
        console.log('Rejoin attempt failed, trying direct lobby join:', error);
      }

      // Try to join the previous lobby directly (this will work even if disconnection timer expired)
      try {
        const response = await socketService.joinLobby(lobbyCode, playerName);

        // Set up event listeners
        setupEventListeners(set, get);

        // Update state
        const actualLobbyCode = response.actualLobbyCode || lobbyCode;
        set({
          lobbyCode: actualLobbyCode,
          playerName,
          isHost: parsedInfo.isHost || false,
          isConnected: true,
          error: null
        });

        // Update localStorage with fresh timestamp after successful rejoin
        const updatedLobbyInfo = {
          lobbyCode: actualLobbyCode,
          playerName,
          isHost: parsedInfo.isHost || false,
          timestamp: Date.now()
        };
        localStorage.setItem('thunee_lobby_info', JSON.stringify(updatedLobbyInfo));
        console.log('Updated lobby info after successful rejoin:', updatedLobbyInfo);

        console.log(`Successfully rejoined lobby ${lobbyCode}`);
        return true;
      } catch (joinError) {
        console.log('Direct lobby join failed:', joinError);

        // Check the specific error message
        const errorMessage = joinError instanceof Error ? joinError.message : 'Unknown error';

        if (errorMessage.includes('Lobby not found')) {
          console.log('Lobby no longer exists, clearing localStorage');
          localStorage.removeItem('thunee_lobby_info');
          return false;
        } else if (errorMessage.includes('Game already in progress')) {
          console.log('Game in progress, checking if we are actually connected...');

          // Wait a moment for any async state updates
          await new Promise(resolve => setTimeout(resolve, 500));

          // Check if we actually got reconnected despite the error
          const currentState = get();
          if (currentState.isConnected && currentState.lobbyCode === lobbyCode) {
            console.log('Successfully reconnected despite "game in progress" error');
            return true;
          }

          console.log('Game in progress but not connected, this is expected for rejoin scenario');
          // Don't clear localStorage for this error - the game might still be active
          return false;
        } else {
          console.log('Other join error, clearing localStorage:', errorMessage);
          localStorage.removeItem('thunee_lobby_info');
          return false;
        }
      }
    } catch (error) {
      console.log('Failed to rejoin previous lobby:', error);
      // Clear invalid lobby info
      localStorage.removeItem('thunee_lobby_info');
      return false;
    }
  },

  hasActiveGameSession: () => {
    // Check if we have lobby info in localStorage that's not too old
    const lobbyInfo = localStorage.getItem('thunee_lobby_info');
    if (!lobbyInfo) return false;

    try {
      const parsedInfo = JSON.parse(lobbyInfo);
      const { timestamp } = parsedInfo;

      // Consider session active if it's within 2 hours
      const twoHours = 2 * 60 * 60 * 1000;
      const isRecent = Date.now() - timestamp < twoHours;

      console.log('hasActiveGameSession: lobbyInfo exists, isRecent:', isRecent, 'age:', (Date.now() - timestamp) / 1000 / 60, 'minutes');
      return isRecent;
    } catch (error) {
      console.error('Error parsing lobby info:', error);
      return false;
    }
  },

  reset: () => {
    socketService.disconnect();
    localStorage.removeItem('thunee_lobby_info');
    set(initialState);
  }
}));

// Set up socket event listeners
function setupEventListeners(
  set: (state: Partial<LobbyState>) => void,
  get: () => LobbyState & LobbyActions
) {
  // Listen for player updates
  socketService.on('players_updated', (data: { players: Player[], teams?: Record<string, Player[]>, teamReady?: { 1: boolean, 2: boolean } }) => {
    const updateData: Partial<LobbyState> = { players: data.players };

    if (data.teamReady) {
      updateData.teamReady = data.teamReady;
    }

    set(updateData);
  });

  // Listen for team ready updates
  socketService.on('team_ready_updated', (data: { teamReady: { 1: boolean, 2: boolean }, players: Player[] }) => {
    set({
      teamReady: data.teamReady,
      players: data.players
    });
  });

  // Listen for team name updates
  socketService.on('team_names_updated', (data: { teamNames: { 1: string, 2: string } }) => {
    set({
      teamNames: data.teamNames
    });
  });

  // Listen for game start
  socketService.on('game_started', () => {
    set({ isGameStarted: true, isFindingMatch: false });

    // Update localStorage timestamp when game starts (this is when rejoin becomes most important)
    const currentState = get();
    if (currentState.lobbyCode && currentState.playerName) {
      const lobbyInfo = {
        lobbyCode: currentState.lobbyCode,
        playerName: currentState.playerName,
        isHost: currentState.isHost,
        timestamp: Date.now() // Update timestamp to current time
      };
      localStorage.setItem('thunee_lobby_info', JSON.stringify(lobbyInfo));
      console.log('Updated lobby info timestamp for game start:', lobbyInfo);
    }
  });

  // Listen for game end to clear rejoin data
  socketService.on('game_ended', () => {
    console.log('Game ended, clearing rejoin data');
    localStorage.removeItem('thunee_lobby_info');
    socketService.clearCurrentGameId();
  });

  // Listen for match status updates
  socketService.on('match_status_update', (data: { isFindingMatch: boolean, matchedLobby: string | null, matchedTeam: Player[] | null, matchedTeamName: string | null }) => {
    console.log('Match status update:', data);
    // Use a timeout to ensure this runs after any other state updates
    setTimeout(() => {
      set({
        isFindingMatch: data.isFindingMatch,
        matchedLobby: data.matchedLobby,
        matchedTeam: data.matchedTeam,
        matchedTeamName: data.matchedTeamName
      });

      // Force a refresh of the page if we have a match
      if (data.matchedLobby && data.matchedTeam) {
        console.log('Match confirmed via status update, forcing UI update');
        // Trigger a UI refresh by toggling a state value
        window.dispatchEvent(new Event('storage')); // Hack to force React to re-render
      }
    }, 500); // Increased timeout for more reliability
  });

  // Listen for match found
  socketService.on('match_found', (data: { matchedLobby: string, matchedTeam: Player[], matchedTeamName: string }) => {
    console.log('Match found!', data);

    // Get current state to debug
    const currentState = get();
    console.log('Current lobby state before match found:', {
      isHost: currentState.isHost,
      players: currentState.players,
      lobbyCode: currentState.lobbyCode
    });

    // Use a timeout to ensure state updates properly
    setTimeout(() => {
      set({
        matchedLobby: data.matchedLobby,
        matchedTeam: data.matchedTeam,
        matchedTeamName: data.matchedTeamName,
        isFindingMatch: false
      });

      // Debug the state after update
      const updatedState = get();
      console.log('Lobby state after match found update:', {
        isHost: updatedState.isHost,
        matchedLobby: updatedState.matchedLobby,
        matchedTeam: updatedState.matchedTeam,
        players: updatedState.players
      });

      console.log('Match found event processed, forcing UI update');
      // Trigger a UI refresh
      window.dispatchEvent(new Event('storage')); // Hack to force React to re-render
    }, 500);
  });

  // Listen for match canceled
  socketService.on('match_canceled', () => {
    console.log('Match finding canceled');
    set({
      matchedLobby: null,
      matchedTeam: null,
      matchedTeamName: null,
      isFindingMatch: false
    });
  });

  // Listen for players updated with finding match status
  socketService.on('players_updated', (data: { players: Player[], isFindingMatch?: boolean }) => {
    console.log('Players updated with finding match status:', data);
    if (data.isFindingMatch !== undefined) {
      set({
        players: data.players,
        isFindingMatch: data.isFindingMatch
      });
    } else {
      set({
        players: data.players
      });
    }
  });

  // Listen for errors
  socketService.on('error', (data: { message: string }) => {
    set({ error: data.message });
  });

  // Listen for disconnection
  socketService.on('disconnect', () => {
    set({ isConnected: false });
  });
}

export default useLobbyStore;
