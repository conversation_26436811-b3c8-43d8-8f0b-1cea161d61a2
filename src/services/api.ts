// API Service for Thunee Frontend
// This service handles all communication with the new layered API backend

import { apiBaseUrl } from '@/config/env';

const API_BASE_URL = apiBaseUrl;

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

interface User {
  id: string;
  username: string;
  email: string;
  isVerified: boolean;
  isAdmin?: boolean;
  statistics?: UserStatistics;
  createdAt: string;
}

interface UserStatistics {
  gamesPlayed: number;
  gamesWon: number;
  gamesLost: number;
  winRate: number;
  totalScore: number;
  averageScore: number;
  handsPlayed: number;
  competitionsJoined: number;
  competitionsWon: number;
  currentRank: number | null;
  bestRank: number | null;
}

interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

interface CompetitionTeam {
  id: string;
  teamName: string;
  player1: { id: string; username: string };
  player2?: { id: string; username: string };
  inviteCode: string;
  gamesPlayed: number;
  points: number;
  bonusPoints: number;
  maxGames: number;
  isComplete: boolean;
  registeredAt: string;
  completedAt?: string;
}

interface CompetitionStatus {
  competitionId: string;
  competitionName: string;
  team?: CompetitionTeam;
  hasTeam: boolean;
  canJoin: boolean;
  canResume: boolean;
  status: string; // waiting_for_partner, ready_to_play, in_progress, completed
}

interface Lobby {
  lobbyCode: string;
  partnerInviteCode: string;
  opponentInviteCode: string;
  teamName: string;
  competitionId?: string;
}

interface LobbyInfo {
  lobbyCode: string;
  teamNames: {
    1: string;
    2: string;
  };
  players: Array<{
    id: string;
    name: string;
    team: number;
    position: number;
    isHost: boolean;
    isReady: boolean;
  }>;
  gameStarted: boolean;
  competitionId?: string;
  isInviteCode?: boolean;
  inviteType?: string;
}

interface Competition {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: 'upcoming' | 'active' | 'completed';
  maxTeams: number;
  currentTeams: number;
  prizes: {
    first: string;
    second: string;
    third: string;
  };
}

interface LeaderboardEntry {
  id: string;
  playerId: string;
  playerName: string;
  score: number;
  rank: number;
  gamesPlayed: number;
  gamesWon: number;
  winRate: number;
}

interface LeaderboardResponse {
  data: LeaderboardEntry[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  filters: {
    timeFrame: string;
    sortBy: string;
  };
}

interface GameSettings {
  playTimeframeOptions: number[];
  defaultPlayTimeframe: number;
  trumperThuneeCallingDuration: number;
  firstRemainingThuneeCallingDuration: number;
  lastRemainingThuneeCallingDuration: number;
  votingTimeLimit: number;
  trumpDisplayDuration: number;
  cardDealingSpeed: number;
  timerUpdateInterval: number;
  cardFaceBaseUrl: string;
  cardBackImageUrl: string;
  customCardFaceMappings?: Record<string, string>;
}

interface UpdateGameSettings {
  playTimeframeOptions?: number[];
  defaultPlayTimeframe?: number;
  trumperThuneeCallingDuration?: number;
  firstRemainingThuneeCallingDuration?: number;
  lastRemainingThuneeCallingDuration?: number;
  votingTimeLimit?: number;
  trumpDisplayDuration?: number;
  cardDealingSpeed?: number;
  timerUpdateInterval?: number;
  cardFaceBaseUrl?: string;
  cardBackImageUrl?: string;
  customCardFaceMappings?: Record<string, string>;
}

class ApiService {
  private token: string | null = null;

  constructor() {
    // Load token from localStorage on initialization
    this.token = localStorage.getItem('authToken');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    // Add authorization header if token exists
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      console.log('Making fetch request to:', url); // Debug log
      const response = await fetch(url, {
        ...options,
        headers,
      });
      console.log('Fetch response received, status:', response.status); // Debug log

      let data;
      try {
        console.log('Attempting to parse JSON...'); // Debug log
        data = await response.json();
       // console.log('Parsed JSON data:', data); // Debug log
      } catch (jsonError) {
      //  console.error('Failed to parse JSON response:', jsonError);
       // console.log('Response status:', response.status);
       // console.log('Response headers:', response.headers);
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      if (!response.ok) {
        // Extract error message from API response format {success: false, error: "message"}
        console.log('API Error Response:', data); // Debug log
        const errorMessage = data.error || data.message || `HTTP error! status: ${response.status}`;
        console.log('Extracted error message:', errorMessage); // Debug log
        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      // If it's a fetch error (network issue), re-throw as is
      if (error instanceof TypeError) {
        console.error('Network error:', error);
        throw new Error('Network error. Please check your connection.');
      }

      // If it's our custom error with the API message, re-throw as is
      if (error instanceof Error) {
        console.error('API request failed:', error.message);
        throw error;
      }

      // Fallback for unknown errors
      console.error('Unknown error:', error);
      throw new Error('An unexpected error occurred');
    }
  }

  // Authentication methods
  async register(username: string, email: string, password: string): Promise<AuthResponse> {
    try {
      const response = await this.request<{success: boolean, data: AuthResponse}>('/auth/register', {
        method: 'POST',
        body: JSON.stringify({ username, email, password }),
      });

      if (response.success && response.data) {
        this.setToken(response.data.token);
        return response.data;
      }
      throw new Error('Registration failed');
    } catch (error) {
      // The error message is already extracted in the request method
      throw error;
    }
  }

  async login(username: string, password: string): Promise<AuthResponse> {
    try {
      console.log('Login method called'); // Debug log
      const response = await this.request<{success: boolean, data: AuthResponse}>('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password }),
      });

      console.log('Login response received:', response); // Debug log
      if (response.success && response.data) {
        this.setToken(response.data.token);
        return response.data;
      }
      throw new Error('Login failed');
    } catch (error) {
      console.log('Login method error:', error); // Debug log
      // The error message is already extracted in the request method
      throw error;
    }
  }

  async logout(): Promise<void> {
    await this.request('/auth/logout', { method: 'POST' });
    this.clearToken();
  }

  async getCurrentUser(): Promise<User> {
    const response = await this.request<User>('/auth/me');
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get current user');
  }

  // Lobby methods
  async createLobby(teamName: string, competitionId?: string): Promise<Lobby> {
    const response = await this.request<Lobby>('/games', {
      method: 'POST',
      body: JSON.stringify({ team1Name: teamName, competitionId }),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to create lobby');
  }

  async getLobby(code: string): Promise<LobbyInfo> {
    const response = await this.request<LobbyInfo>(`/games/${code}`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get lobby');
  }

  async joinLobby(code: string, playerName: string): Promise<{ actualLobbyCode: string; team: number; isInviteCode: boolean }> {
    const response = await this.request<{ actualLobbyCode: string; team: number; isInviteCode: boolean }>(`/games/${code}/join`, {
      method: 'POST',
      body: JSON.stringify({ playerName }),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to join lobby');
  }

  async leaveLobby(code: string): Promise<void> {
    await this.request(`/games/${code}/leave`, { method: 'POST' });
  }

  async setPlayerReady(code: string, ready: boolean): Promise<void> {
    await this.request(`/games/${code}/ready`, {
      method: 'POST',
      body: JSON.stringify({ ready }),
    });
  }

  async startGame(code: string): Promise<any> {
    const response = await this.request(`/games/${code}/start`, { method: 'POST' });
    return response.data;
  }

  async getActiveLobbies(): Promise<Array<{
    lobbyCode: string;
    teamName: string;
    playerCount: number;
    gameStarted: boolean;
    competitionId?: string;
    isHost: boolean;
  }>> {
    const response = await this.request<Array<any>>('/games/active');
    return response.data || [];
  }

  // Game methods
  async getUserGames(page = 1, limit = 20, status?: string): Promise<{ games: any[]; pagination: any }> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });
    
    if (status) {
      params.append('status', status);
    }

    const response = await this.request<any[]>(`/games?${params}`);
    return {
      games: response.data || [],
      pagination: (response as any).pagination || {}
    };
  }

  // User methods
  async getUserProfile(): Promise<User> {
    const response = await this.request<User>('/users/profile');
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get user profile');
  }

  async updateProfile(username?: string, email?: string): Promise<User> {
    const response = await this.request<User>('/users/profile', {
      method: 'PUT',
      body: JSON.stringify({ username, email }),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to update profile');
  }

  // Competition methods
  async getCompetitions(): Promise<Competition[]> {
    const response = await this.request<Competition[]>('/competitions');
    return response.data || [];
  }

  async getCompetition(id: string): Promise<Competition> {
    const response = await this.request<Competition>(`/competitions/${id}`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get competition');
  }

  // Competition team management
  async createCompetitionTeam(competitionId: string, teamName: string): Promise<CompetitionTeam> {
    const response = await this.request<CompetitionTeam>(`/competitions/${competitionId}/teams/create`, {
      method: 'POST',
      body: JSON.stringify({ teamName }),
    });
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to create competition team');
  }

  async joinCompetitionTeam(inviteCode: string): Promise<CompetitionTeam> {
    const response = await this.request<CompetitionTeam>('/competitions/teams/join', {
      method: 'POST',
      body: JSON.stringify({ inviteCode }),
    });
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to join competition team');
  }

  async getCompetitionStatus(competitionId: string): Promise<CompetitionStatus> {
    const response = await this.request<CompetitionStatus>(`/competitions/${competitionId}/status`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get competition status');
  }

  async joinCompetition(id: string): Promise<void> {
    await this.request(`/competitions/${id}/join`, { method: 'POST' });
  }

  async getCompetitionLeaderboard(id: string): Promise<LeaderboardEntry[]> {
    const response = await this.request<LeaderboardEntry[]>(`/competitions/${id}/leaderboard`);
    return response.data || [];
  }

  // Leaderboard methods
  async getGlobalLeaderboard(timeFrame = 'all', sortBy = 'score', page = 1, limit = 20): Promise<LeaderboardResponse> {
    const params = new URLSearchParams({
      timeFrame,
      sortBy,
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await this.request<LeaderboardResponse>(`/leaderboard/global?${params}`);
    return response.data || { data: [], pagination: { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 20 }, filters: { timeFrame, sortBy } };
  }

  async getWeeklyLeaderboard(page = 1, limit = 20): Promise<LeaderboardResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await this.request<LeaderboardResponse>(`/leaderboard/weekly?${params}`);
    return response.data || { data: [], pagination: { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 20 }, filters: { timeFrame: 'weekly', sortBy: 'score' } };
  }

  async getMonthlyLeaderboard(page = 1, limit = 20): Promise<LeaderboardResponse> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await this.request<LeaderboardResponse>(`/leaderboard/monthly?${params}`);
    return response.data || { data: [], pagination: { currentPage: 1, totalPages: 1, totalItems: 0, itemsPerPage: 20 }, filters: { timeFrame: 'monthly', sortBy: 'score' } };
  }

  // Game Settings methods
  async getGameSettings(): Promise<GameSettings> {
    const response = await this.request<GameSettings>('/gamesettings');
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get game settings');
  }

  async updateGameSettings(settings: UpdateGameSettings): Promise<GameSettings> {
    const response = await this.request<GameSettings>('/gamesettings', {
      method: 'PUT',
      body: JSON.stringify(settings),
    });

    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to update game settings');
  }

  async resetGameSettings(): Promise<GameSettings> {
    const response = await this.request<GameSettings>('/gamesettings/reset', {
      method: 'POST',
    });

    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to reset game settings');
  }

  // Token management
  private setToken(token: string): void {
    this.token = token;
    localStorage.setItem('authToken', token);
  }

  private clearToken(): void {
    this.token = null;
    localStorage.removeItem('authToken');
  }

  isAuthenticated(): boolean {
    return !!this.token;
  }

  getToken(): string | null {
    return this.token;
  }

  // Admin API Methods

  // User Management
  async getAllUsers(): Promise<AdminUser[]> {
    const response = await this.request<AdminUser[]>('/admin/users');
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get users');
  }

  async getUserById(userId: string): Promise<AdminUser> {
    const response = await this.request<AdminUser>(`/admin/users/${userId}`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get user');
  }

  async updateUser(userId: string, updateData: AdminUpdateUser): Promise<AdminUser> {
    const response = await this.request<AdminUser>(`/admin/users/${userId}`, {
      method: 'POST',
      body: JSON.stringify(updateData),
    });
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to update user');
  }

  async deleteUser(userId: string): Promise<void> {
    await this.request(`/admin/users/${userId}`, {
      method: 'DELETE',
    });
  }

  async changeUserPassword(userId: string, newPassword: string): Promise<void> {
    await this.request(`/admin/users/${userId}/password`, {
      method: 'POST',
      body: JSON.stringify({ newPassword }),
    });
  }

  // Competition Management
  async getAllCompetitions(): Promise<AdminCompetition[]> {
    const response = await this.request<AdminCompetition[]>('/admin/competitions');
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get competitions');
  }

  async getCompetitionById(competitionId: string): Promise<AdminCompetition> {
    const response = await this.request<AdminCompetition>(`/admin/competitions/${competitionId}`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get competition');
  }

  async createCompetition(competitionData: any): Promise<AdminCompetition> {
    const response = await this.request<AdminCompetition>('/admin/competitions', {
      method: 'POST',
      body: JSON.stringify(competitionData),
    });
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to create competition');
  }

  async updateCompetition(competitionId: string, updateData: any): Promise<AdminCompetition> {
    const response = await this.request<AdminCompetition>(`/admin/competitions/${competitionId}`, {
      method: 'POST',
      body: JSON.stringify(updateData),
    });
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to update competition');
  }

  async deleteCompetition(competitionId: string): Promise<void> {
    await this.request(`/admin/competitions/${competitionId}`, {
      method: 'DELETE',
    });
  }

  // Competition Teams Management
  async getCompetitionTeams(competitionId: string): Promise<AdminCompetitionTeam[]> {
    const response = await this.request<AdminCompetitionTeam[]>(`/admin/competitions/${competitionId}/teams`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get competition teams');
  }

  async deleteCompetitionTeam(teamId: string): Promise<void> {
    await this.request(`/admin/teams/${teamId}`, {
      method: 'DELETE',
    });
  }

  // Games Management
  async getCompetitionGames(competitionId: string): Promise<AdminGame[]> {
    const response = await this.request<AdminGame[]>(`/admin/competitions/${competitionId}/games`);
    if (response.data) {
      return response.data;
    }
    throw new Error('Failed to get competition games');
  }

  // Email Management
  async sendCompetitionEmail(competitionId: string, emailData: CompetitionEmail): Promise<void> {
    await this.request(`/admin/competitions/${competitionId}/email`, {
      method: 'POST',
      body: JSON.stringify(emailData),
    });
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;

// Admin interfaces
interface AdminUser {
  id: string;
  username: string;
  email: string;
  isVerified: boolean;
  isActive: boolean;
  isAdmin: boolean;
  lastLoginAt: string | null;
  createdAt: string;
  updatedAt: string;
}

interface AdminUpdateUser {
  username?: string;
  email?: string;
  isVerified?: boolean;
  isActive?: boolean;
  isAdmin?: boolean;
}

interface ChangeUserPassword {
  newPassword: string;
}

interface AdminCompetition {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  status: string;
  maxTeams: number;
  currentTeams: number;
  entryFee: number;
  prizeFirst: number;
  prizeSecond: number;
  prizeThird: number;
  totalPrizePool: number;
  rules: string;
  isPublic: boolean;
  allowSpectators: boolean;
  maxGamesPerTeam: number;
  createdAt: string;
  updatedAt: string;
}

interface AdminCompetitionTeam {
  id: string;
  competitionId: string;
  teamName: string;
  player1Id: string;
  player1Username: string;
  player1Email: string;
  player2Id: string;
  player2Username: string;
  player2Email: string;
  inviteCode: string;
  gamesPlayed: number;
  points: number;
  bonusPoints: number;
  maxGames: number;
  isActive: boolean;
  isComplete: boolean;
  registeredAt: string;
  completedAt: string | null;
}

interface AdminGame {
  id: string;
  lobbyCode: string;
  competitionId: string | null;
  competitionName: string | null;
  team1Name: string;
  team2Name: string;
  status: string;
  team1Score: number;
  team2Score: number;
  team1BallsWon: number;
  team2BallsWon: number;
  winnerTeam: number | null;
  startedAt: string | null;
  completedAt: string | null;
  createdAt: string;
}

interface CompetitionEmail {
  subject: string;
  message: string;
}

// Export types for use in other files
export type {
  User,
  AuthResponse,
  Lobby,
  LobbyInfo,
  Competition,
  LeaderboardEntry,
  LeaderboardResponse,
  GameSettings,
  UpdateGameSettings,
  AdminUser,
  AdminUpdateUser,
  ChangeUserPassword,
  AdminCompetition,
  AdminCompetitionTeam,
  AdminGame,
  CompetitionEmail
};
