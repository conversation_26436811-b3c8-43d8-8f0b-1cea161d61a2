/* BallResultsDisplay.css */

.double-results-container {
  position: fixed;
  inset: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.9);
  overflow: auto;
}

.double-results-card-wrapper {
  width: 100%;
  max-width: 48rem; /* 768px */
  padding: 0.5rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100%;
}

.double-results-card {
  background-color: black;
  border: 2px solid #E1C760;
  border-radius: 0.5rem;
  padding: 1.5rem;
  width: 100%;
  color: white;
  overflow: auto;
  max-height: 90vh;
}

.double-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.double-results-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #E1C760;
}

.double-results-timer {
  background-color: #E1C760;
  color: black;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: bold;
}

.double-results-message {
  font-size: 1.25rem;
  text-align: center;
  margin-bottom: 1rem;
}

.double-results-content {
  margin-bottom: 1.5rem;
}

.double-results-section {
  margin-bottom: 1.5rem;
}

.double-results-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #E1C760;
  margin-bottom: 0.5rem;
}

.double-results-hands-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.double-results-hand-card {
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid #374151;
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.double-results-hand-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.double-results-hand-winner {
  color: #E1C760;
}

.double-results-cards-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

.double-results-card-image {
  width: 3rem;
  height: 4rem;
  position: relative;
}

.double-results-card-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.double-results-last-hand {
  background-color: rgba(17, 24, 39, 0.5);
  border: 1px solid #374151;
  border-radius: 0.5rem;
  padding: 1rem;
}

.double-results-last-hand-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.double-results-winner-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.double-results-winner-badge-team1 {
  background-color: #1e3a8a; /* bg-blue-900 */
  color: #bfdbfe; /* text-blue-200 */
}

.double-results-winner-badge-team2 {
  background-color: #7f1d1d; /* bg-red-900 */
  color: #fecaca; /* text-red-200 */
}

.double-results-last-hand-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.75rem;
}

.double-results-last-hand-card {
  width: 4rem;
  height: 6rem;
  position: relative;
  border-radius: 0.25rem;
}

.double-results-last-hand-card img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 0.25rem;
}

.double-results-last-hand-card-border {
  border: 1px solid #374151;
}

.double-results-last-hand-card-winner {
  border: 2px solid #E1C760;
}

.double-results-winner-checkmark {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background-color: #E1C760;
  color: black;
  font-size: 0.75rem;
  font-weight: bold;
  border-radius: 9999px;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.double-results-final {
  background-color: rgba(225, 199, 96, 0.2);
  border: 2px solid #E1C760;
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-align: center;
  margin-bottom: 1rem;
}

.double-results-final-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #E1C760;
  margin-bottom: 0.75rem;
}

.double-results-final-result {
  font-size: 1.875rem;
  font-weight: bold;
}

.double-results-final-description {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  opacity: 0.8;
}

.double-results-continue-button {
  background-color: #E1C760;
  color: black;
  font-weight: bold;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1.25rem;
  transition: all 0.2s;
  cursor: pointer;
}

.double-results-continue-button:hover {
  background-color: rgba(225, 199, 96, 0.8);
}

/* Media queries for responsive design */
@media (min-width: 768px) {
  .double-results-hands-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .double-results-card {
    padding: 2rem;
  }
  
  .double-results-title {
    font-size: 2rem;
  }
}

/* Mobile landscape orientation */
@media (max-width: 915px) and (max-height: 450px) and (orientation: landscape) {
  .double-results-card-wrapper {
    padding: 0.25rem;
    align-items: flex-start;
  }
  
  .double-results-card {
    padding: 0.75rem;
    max-height: 85vh;
    font-size: 0.875rem;
  }
  
  .double-results-title {
    font-size: 1.25rem;
  }
  
  .double-results-message {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .double-results-section {
    margin-bottom: 0.75rem;
  }
  
  .double-results-section-title {
    font-size: 1rem;
    margin-bottom: 0.25rem;
  }
  
  .double-results-hands-grid {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }
  
  .double-results-card-image {
    width: 2rem;
    height: 3rem;
  }
  
  .double-results-last-hand {
    padding: 0.5rem;
  }
  
  .double-results-last-hand-card {
    width: 2.5rem;
    height: 3.5rem;
  }
  
  .double-results-final {
    padding: 0.75rem;
  }
  
  .double-results-final-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .double-results-final-result {
    font-size: 1.25rem;
  }
  
  .double-results-continue-button {
    padding: 0.5rem 1rem;
    font-size: 1rem;
  }
}

/* Mobile portrait orientation */
@media (max-width: 480px) {
  .double-results-card {
    padding: 1rem;
  }
  
  .double-results-title {
    font-size: 1.25rem;
  }
  
  .double-results-message {
    font-size: 1.125rem;
  }
  
  .double-results-last-hand-card {
    width: 3rem;
    height: 4.5rem;
  }
  
  .double-results-final {
    padding: 1rem;
  }
  
  .double-results-final-result {
    font-size: 1.5rem;
  }
  
  .double-results-continue-button {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
  }
}
