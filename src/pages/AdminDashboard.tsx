"use client";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Users, Trophy, Settings, Image, Clock, Mail } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/store/authStore";
import { useEffect } from "react";

export default function AdminDashboard() {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();

  // Redirect if not admin
  useEffect(() => {
    if (!isAuthenticated || !user?.isAdmin) {
      navigate("/");
    }
  }, [isAuthenticated, user, navigate]);

  if (!isAuthenticated || !user?.isAdmin) {
    return null;
  }

  const adminSections = [
    {
      title: "Time Settings",
      description: "Configure game timing and timeframes",
      icon: <Clock className="h-8 w-8" />,
      path: "/admin/time-settings",
      color: "bg-blue-500/10 border-blue-500/30 text-blue-400"
    },
    {
      title: "Card Images",
      description: "Manage card face and back images",
      icon: <Image className="h-8 w-8" />,
      path: "/admin/card-settings",
      color: "bg-green-500/10 border-green-500/30 text-green-400"
    },
    {
      title: "Competition Management",
      description: "Create, update, and manage competitions",
      icon: <Trophy className="h-8 w-8" />,
      path: "/admin/competitions",
      color: "bg-yellow-500/10 border-yellow-500/30 text-yellow-400"
    },
    {
      title: "User Management",
      description: "View and manage registered players",
      icon: <Users className="h-8 w-8" />,
      path: "/admin/users",
      color: "bg-purple-500/10 border-purple-500/30 text-purple-400"
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/")}
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <Settings className="h-8 w-8 text-[#E1C760]" />
            <h1 className="text-3xl font-bold text-[#E1C760]">Admin Dashboard</h1>
          </div>
        </div>

        {/* Welcome Message */}
        <div className="mb-8">
          <Card className="bg-[#E1C760]/10 border-[#E1C760]/30">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-[#E1C760] mb-2">
                Welcome, {user.username}!
              </h2>
              <p className="text-gray-300">
                You have administrative access to manage the Thunee platform. Use the sections below to configure settings, manage competitions, and oversee user accounts.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Admin Sections Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {adminSections.map((section) => (
            <Card 
              key={section.title}
              className={`${section.color} cursor-pointer hover:scale-105 transition-transform duration-200`}
              onClick={() => navigate(section.path)}
            >
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  {section.icon}
                  {section.title}
                </CardTitle>
                <CardDescription className="text-gray-300">
                  {section.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  variant="ghost" 
                  className="w-full justify-start text-left hover:bg-white/10"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate(section.path);
                  }}
                >
                  Manage →
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="mt-8">
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Quick Actions</CardTitle>
              <CardDescription className="text-gray-400">
                Common administrative tasks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  variant="outline"
                  className="border-[#E1C760]/30 text-[#E1C760] hover:bg-[#E1C760]/10"
                  onClick={() => navigate("/admin/competitions/create")}
                >
                  <Trophy className="h-4 w-4 mr-2" />
                  Create Competition
                </Button>
                <Button 
                  variant="outline"
                  className="border-[#E1C760]/30 text-[#E1C760] hover:bg-[#E1C760]/10"
                  onClick={() => navigate("/admin/users")}
                >
                  <Users className="h-4 w-4 mr-2" />
                  View All Users
                </Button>
                <Button 
                  variant="outline"
                  className="border-[#E1C760]/30 text-[#E1C760] hover:bg-[#E1C760]/10"
                  onClick={() => navigate("/admin/time-settings")}
                >
                  <Clock className="h-4 w-4 mr-2" />
                  Update Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
