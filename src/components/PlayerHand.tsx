import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore, Card as PlayingCard } from "@/store/gameStore";
import { getCardImagePath } from "@/utils/cardUtils";

interface PlayerHandProps {
  cards?: PlayingCard[];
}

export default function PlayerHand({ cards = [] }: PlayerHandProps) {
  const { playCard, playedCards: storePlayedCards, isCurrentTurn, hands } = useGameStore();
  const [localHand, setLocalHand] = useState<PlayingCard[]>([]);
  const [playedCard, setPlayedCard] = useState<PlayingCard | null>(null);
  // const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  const [validCards, setValidCards] = useState<PlayingCard[]>([]);

  // Filter out any cards that are currently being played or have been played
  const filteredCards = cards.filter(card => {
    // If this card is currently being animated as played, filter it out
    if (playedCard && playedCard.id === card.id) return false;

    // If this card is in the store's played cards, filter it out
    if (storePlayedCards.some(pc => pc.id === card.id)) return false;

    // Check if this card is in any completed hands
    const { hands } = useGameStore.getState();
    const isInCompletedHands = hands.some(hand =>
      hand.cards.some(handCard => handCard.id === card.id)
    );
    if (isInCompletedHands) return false;

    return true;
  });

  // Use filtered cards from props if available, otherwise use localHand
  const displayCards = cards.length > 0 ? filteredCards : localHand;

  // Check if we need to show the scroll indicator
  // useEffect(() => {
  //   setShowScrollIndicator(displayCards.length > 4);
  // }, [displayCards.length]);

  // Function to check for duplicate cards
  const hasDuplicates = (cardsToCheck: PlayingCard[]): boolean => {
    const cardKeys = new Set<string>();
    for (const card of cardsToCheck) {
      const key = `${card.value}_${card.suit}`;
      if (cardKeys.has(key)) {
        console.error(`Duplicate card found in PlayerHand: ${card.value} of ${card.suit}`);
        return true;
      }
      cardKeys.add(key);
    }
    return false;
  };

  // Function to remove duplicates while preserving order
  const removeDuplicates = (cardsToCheck: PlayingCard[]): PlayingCard[] => {
    const uniqueCards: PlayingCard[] = [];
    const cardKeys = new Set<string>();

    for (const card of cardsToCheck) {
      const key = `${card.value}_${card.suit}`;
      if (!cardKeys.has(key)) {
        cardKeys.add(key);
        uniqueCards.push(card);
      } else {
        console.warn(`Skipping duplicate card in PlayerHand: ${card.value} of ${card.suit}`);
      }
    }

    return uniqueCards;
  };

  // Update localHand when cards prop changes
  useEffect(() => {
    console.log('PlayerHand received cards:', cards);

    // Only process if we have cards and they're different from what we already have
    if (cards.length > 0 && JSON.stringify(cards) !== JSON.stringify(localHand)) {
      // Check for duplicates
      if (hasDuplicates(cards)) {
        console.warn('Duplicates found in cards passed to PlayerHand, removing duplicates');
        const uniqueCards = removeDuplicates(cards);
        console.log('Unique cards in PlayerHand:', uniqueCards);
        setLocalHand(uniqueCards);
      } else if (localHand.length === 0 || localHand.length < cards.length) {
        console.log('Updating localHand with cards from props');
        setLocalHand(cards);
      }
    }
  }, [cards]); // Only depend on cards prop, not on derived state

  // Re-filter cards when hands change (to remove cards that were in completed hands)
  useEffect(() => {
    if (hands.length > 0 && localHand.length > 0) {
      console.log('Hands changed, re-filtering local hand');
      // Filter out cards that are in completed hands
      const filteredLocalHand = localHand.filter(card => {
        const isInCompletedHands = hands.some(hand =>
          hand.cards.some(handCard => handCard.id === card.id)
        );
        return !isInCompletedHands;
      });

      if (filteredLocalHand.length !== localHand.length) {
        console.log(`Removed ${localHand.length - filteredLocalHand.length} cards from local hand that were in completed hands`);
        setLocalHand(prevHand => {
          // Only update if the filtered hand is different from the current state
          // This helps prevent infinite loops
          if (JSON.stringify(prevHand) !== JSON.stringify(filteredLocalHand)) {
            return filteredLocalHand;
          }
          return prevHand;
        });
      }
    }
  }, [hands]); // Only depend on hands, not localHand to avoid infinite loop

  // Determine valid cards to play based on the current state of the game
  useEffect(() => {
    if (!isCurrentTurn || displayCards.length === 0) {
      setValidCards([]);
      return;
    }

    // Modified rule: All cards are valid regardless of lead suit
    console.log('All cards are valid - player can play any card');

    // Use a function to update state to prevent infinite loops
    setValidCards(prevCards => {
      // Only update if the cards have actually changed
      if (prevCards.length !== displayCards.length ||
          JSON.stringify(prevCards) !== JSON.stringify(displayCards)) {
        return displayCards;
      }
      return prevCards;
    });

  }, [displayCards, isCurrentTurn]);

  // Handle playing a card
  const handlePlayCard = (card: PlayingCard) => {
    console.log('handlePlayCard called with card:', card);
    console.log('Current turn state:', { isCurrentTurn, validCards: validCards.length });

    // Check if it's the player's turn
    if (!isCurrentTurn) {
      console.log("Not your turn! Cannot play card.");
      return;
    }

    // All cards are valid to play with the new rule
    // No need to check if the card follows suit

    console.log('Card is valid, proceeding to play it');

    // Make sure the card has the current player's ID
    const currentPlayerId = useGameStore.getState().currentTurn;
    console.log(`Playing card with player ID: ${currentPlayerId}`);

    // Create a copy of the card with the player ID
    const cardWithPlayer = {
      ...card,
      playedBy: currentPlayerId
    };

    setPlayedCard(cardWithPlayer);

    // Immediately remove the card from localHand
    if (cards.length === 0) { // Only update localHand if we're using it
      setLocalHand(prevHand => prevHand.filter(c => c.id !== card.id));
    }

    // Add card to played cards after animation
    setTimeout(() => {
      setPlayedCard(null);

      console.log('Animation complete, sending card to server...');
      // Send the card with player ID to the server
      playCard(cardWithPlayer);
    }, 500);
  };

  // We no longer need the PlayedCardsPortal as we're using the stacked cards approach
  // in the PlayedCardsArrangement component

  return (
    <>

      {/* Animating Card */}
      <AnimatePresence mode="wait">
        {playedCard && (
          <motion.div
            key={playedCard.id}
            initial={{ y: 0, x: 0, opacity: 1 }}
            animate={{
              y: -window.innerHeight/2 + 60, // Animate to middle of top half
              opacity: 1
            }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            style={{
              position: "fixed",
              left: "50%",
              top: "50%",
              transform: "translate(-50%, -50%)",
              zIndex: 50,
            }}
            className="w-[var(--card-width-sm)] sm:w-[var(--card-width-md)] md:w-[var(--card-width-lg)]"
          >
            <Card className="aspect-[2/3] w-full flex items-center justify-center bg-white">
              <img
                src={getCardImagePath(playedCard.value, playedCard.suit)}
                alt={`${playedCard.value} of ${playedCard.suit}`}
                className="w-full h-full object-contain"
              />
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      {!isCurrentTurn && (
        <div className="-mt-4 text-center">
          <span className="text-white bg-black/50 px-3 py-1 rounded-full text-sm">
            Waiting for other players...
          </span>
        </div>
      )}
      {/* Player's Hand */}
      <div className="w-full flex items-center justify-center">
        <div className="w-full max-w-4xl flex justify-center gap-2 sm:gap-3 px-2 pb-4 relative">
          {/* {showScrollIndicator && <ScrollIndicator />} */}
          <AnimatePresence>
            {displayCards.map((card) => {
              const isValid = validCards.some(c => c.id === card.id);
              const isDisabled = !isCurrentTurn || (validCards.length > 0 && !isValid);

              return (
                <motion.button
                  key={card.id}
                  layout
                  initial={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  onClick={() => handlePlayCard(card)}
                  disabled={isDisabled}
                  className={`transform ${!isDisabled ? 'hover:-translate-y-2' : ''} transition-transform duration-200 flex-shrink-0
                  w-[var(--card-width-xs)] sm:w-[var(--card-width-sm)] md:w-[var(--card-width-md)] lg:w-[var(--card-width-lg)] player-card
                  ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''} ${validCards.length > 0 && isValid ? 'ring-2 ring-[#E1C760]' : ''}`}
                >
                  <Card className={`aspect-[2/3] w-full flex items-center justify-center bg-white
                    ${!isDisabled ? 'hover:shadow-lg' : ''}`}>
                    <img
                      src={getCardImagePath(card.value, card.suit)}
                      alt={`${card.value} of ${card.suit}`}
                      className="w-full h-full object-contain"
                    />
                  </Card>
                </motion.button>
              );
            })}
          </AnimatePresence>
        </div>
      </div>


    </>
  );
}
