"use client";
import { useState } from "react";
import { Menu } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "@/store/authStore";

type MenuItem = {
  title: string;
  icon?: React.ReactNode;
  isToggle?: boolean;
  isToggleOn?: boolean;
};

export default function BurgerMenu() {
  const [selectedItem, setSelectedItem] = useState<string>("Game rules");
  const [open, setOpen] = useState(false);
  const [toggleStates, setToggleStates] = useState([true, true]);
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();

  // All menu items
  const allMenuItems: MenuItem[] = [
    {
      title: "Sound",
      isToggle: true,
      isToggleOn: toggleStates[0],
      icon: <span className={`text-xl ${toggleStates[0] ? 'text-green-500' : 'text-red-500'}`}>🔊</span>
    },
    {
      title: "Music",
      isToggle: true,
      isToggleOn: toggleStates[1],
      icon: <span className={`text-xl ${toggleStates[1] ? 'text-green-500' : 'text-red-500'}`}>🎵</span>
    },
    {
      title: "Game Rules",
      icon: <span className="text-white text-xl">🎮</span>
    },
    {
      title: "Bet History",
      icon: <span className="text-white text-xl">📊</span>
    },
    {
      title: "Players In Game",
      icon: <span className="text-white text-xl">👥</span>
    },
    {
      title: "Promotions",
      icon: <span className="text-white text-xl">🏆</span>
    },
    {
      title: "Chat",
      icon: <span className="text-white text-xl">💬</span>
    },
    {
      title: "Language",
      icon: <span className="text-white text-xl">🌐</span>
    },
    {
      title: "Time Settings",
      icon: <span className="text-white text-xl">⏱️</span>
    },
    {
      title: "Card Images",
      icon: <span className="text-white text-xl">🃏</span>
    },
    {
      title: "Exit",
      icon: <span className="text-white text-xl">🚪</span>
    },
  ];

  // Filter menu items based on authentication status
  const menuItems = allMenuItems.filter(item => {
    // Hide these items if user is not authenticated
    if (!isAuthenticated && (
      item.title === "Bet History" ||
      item.title === "Chat" ||
      item.title === "Players In Game"
    )) {
      return false;
    }
    return true;
  });

  const gameRulesContent = [
    {
      title: "Recent Results",
      content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
    },
    {
      title: "Payouts",
      content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
    },
    {
      title: "Return to Player",
      content: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
    }
  ];

  const handleToggleClick = (index: number) => {
    const newToggleStates = [...toggleStates];
    newToggleStates[index] = !newToggleStates[index];
    setToggleStates(newToggleStates);
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.title === "Exit") {
      setOpen(false); // Close the menu
      navigate("/"); // Navigate to home
    } else if (item.title === "Time Settings") {
      setOpen(false); // Close the menu
      navigate("/settings"); // Navigate to time settings
    } else if (item.title === "Card Images") {
      setOpen(false); // Close the menu
      navigate("/card-settings"); // Navigate to card image settings
    } else if (item.isToggle) {
      // Find the index in the original allMenuItems array for toggle handling
      const originalIndex = allMenuItems.findIndex(originalItem => originalItem.title === item.title);
      handleToggleClick(originalIndex);
    } else {
      setSelectedItem(item.title);
    }
  };

  return (
      <div className="relative">
        <Sheet open={open} onOpenChange={setOpen}>
          <div
              className={cn(
                  "fixed transition-all duration-300 ease-in-out top-0 left-0 z-[60] flex h-16 items-center px-4",
                  open
                      ? "bg-black border-b border-neutral-800 w-16"
                      : "bg-transparent w-16"
              )}
          >
            <SheetTrigger asChild>
              <button className="mr-6">
                <Menu className="h-6 w-6 text-white bg-blackglass" />
              </button>
            </SheetTrigger>
          </div>
          <SheetContent
              side="left"
              className="p-0 border-r border-neutral-800 bg-blackglass/90 backdrop-opacity-90 w-[80%] sm:max-w-[80%] md:max-w-[70%]"
          >
            <div className="flex h-full">
              {/* Menu Items Panel - made smaller */}
              <div className=" w-3/6 border-r border-neutral-800">
                <div className="flex flex-col pt-16">
                  {menuItems.map((item, index) => (
                      <motion.div
                          key={item.title}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className={`py-2 px-2 border-b border-neutral-800/30 ${selectedItem === item.title && !item.isToggle ? 'bg-neutral-800/20' : ''}`}
                      >
                        <button
                            onClick={() => handleItemClick(item)}
                            className="w-full flex items-center justify-between"
                        >
                          <div className="flex items-center gap-1">
                            {item.icon}
                            <span className="text-white text-xs whitespace-nowrap">{item.title}</span>
                          </div>

                          {item.isToggle && (
                              <div className={`w-7 h-3.5 rounded-full transition-colors ${toggleStates[index] ? 'bg-green-500' : 'bg-red-500'} flex items-center p-0.5`}>
                                <motion.div
                                    className="w-2 h-2 bg-white rounded-full"
                                    animate={{ x: toggleStates[index] ? 12 : 0 }}
                                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                                />
                              </div>
                          )}
                        </button>
                      </motion.div>
                  ))}
                </div>
              </div>

              {/* Content Panel - made bigger */}
              <div className="w-[100vw] py-6 px-6 overflow-y-auto">
                <AnimatePresence mode="wait">
                  {selectedItem === "Game rules" && (
                      <motion.div
                          key="game-rules"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                          className="space-y-6"
                      >
                        {gameRulesContent.map((section, index) => (
                            <div key={section.title} className="mb-6">
                              <h2 className="text-white font-semibold mb-2">
                                {section.title}
                              </h2>
                              <p className="text-xs text-neutral-400">
                                {section.content}
                              </p>
                            </div>
                        ))}
                      </motion.div>
                  )}

                  {selectedItem === "Bet History" && isAuthenticated && (
                      <motion.div
                          key="bet-history"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                      >
                        <h2 className="text-white font-semibold mb-4">Bet History</h2>
                        <div className="text-xs text-neutral-400">
                          <p>Your recent betting activity will appear here.</p>
                          <div className="mt-4 p-3 border border-neutral-700 rounded">
                            <div className="mb-2 font-medium text-white">Winner of Next Hand</div>
                            <div className="text-neutral-300">5 Ball Bet</div>
                          </div>
                          <div className="mt-4 grid grid-cols-4 gap-2">
                            <div className="col-span-1">Call</div>
                            <div className="col-span-1">Bet</div>
                            <div className="col-span-2">Fold</div>
                          </div>
                        </div>
                      </motion.div>
                  )}

                  {selectedItem !== "Game rules" && selectedItem !== "Bet History" && (
                      <motion.div
                          key={selectedItem}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={{ duration: 0.2 }}
                      >
                        <h2 className="text-white font-semibold mb-4">{selectedItem}</h2>
                        <p className="text-xs text-neutral-400">
                          Content for {selectedItem} will be displayed here.
                        </p>
                      </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
  );
}