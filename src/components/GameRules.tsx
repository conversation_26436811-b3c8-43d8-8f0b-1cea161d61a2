// import { useState } from "react";
// import { Button } from "@/components/ui/button";
// import { X } from "lucide-react";

// interface GameRulesProps {
//   onClose: () => void;
//   onContinue?: () => void;
// }

// export default function GameRules({ onClose, onContinue }: GameRulesProps) {
//   const [currentSection, setCurrentSection] = useState(0);

//   const sections = [
//     {
//       title: "HOW THE GAME IS PLAYED",
//       content: (
//         <>
//           <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Game Overview</h4>
//           <p>Thunee is a trick-taking card game played with 4 people, divided into two teams of 2 players each.</p>
//           <p>Your partner sits opposite you at the table and your opponents are to the left and right of you.</p>

//           <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Team Roles</h4>
//           <p>There are two main roles in the game:</p>
//           <ul className="list-disc pl-5 space-y-1 mb-3">
//             <li><strong>Scoring Team:</strong> Your aim is to win 105 points worth of cards.</li>
//             <li><strong>Trumping Team:</strong> Your aim is to stop your opponents from counting to 105 points.</li>
//           </ul>

//           <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Game Flow</h4>
//           <ol className="list-decimal pl-5 space-y-1">
//             <li>Determine the dealer (first player to receive a black Jack)</li>
//             <li>The dealer offers the deck to the player on their left to cut</li>
//             <li>The dealer distributes 4 cards to each player</li>
//             <li>The player to the dealer's right selects the trump suit</li>
//             <li>Bidding phase occurs (optional)</li>
//             <li>The dealer distributes 2 more cards to each player</li>
//             <li>Players play tricks, following suit when possible</li>
//             <li>After all 6 tricks are played, points are tallied</li>
//             <li>If the scoring team reaches 105+ points, they win a ball; otherwise, the trumping team wins</li>
//           </ol>

//           <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Winning the Game</h4>
//           <p>By achieving your team's aim, you will be awarded a 'ball' (game point). The first team to win 13 balls wins the game.</p>
//         </>
//       )
//     },
//     {
//       title: "POINT VALUES OF THE CARDS",
//       content: (
//         <>
//           <div className="grid grid-cols-2 gap-4 mb-4">
//             <div className="flex items-center">
//               <img src="/CardFace/JS.svg" alt="Jack" className="w-10 h-14 mr-2" />
//               <span>Jack = 30 points</span>
//             </div>
//             <div className="flex items-center">
//               <img src="/CardFace/9S.svg" alt="Nine" className="w-10 h-14 mr-2" />
//               <span>Nine = 20 points</span>
//             </div>
//             <div className="flex items-center">
//               <img src="/CardFace/AS.svg" alt="Ace" className="w-10 h-14 mr-2" />
//               <span>Ace = 11 points</span>
//             </div>
//             <div className="flex items-center">
//               <img src="/CardFace/TS.svg" alt="Ten" className="w-10 h-14 mr-2" />
//               <span>Ten = 10 points</span>
//             </div>
//             <div className="flex items-center">
//               <img src="/CardFace/KS.svg" alt="King" className="w-10 h-14 mr-2" />
//               <span>King = 3 points</span>
//             </div>
//             <div className="flex items-center">
//               <img src="/CardFace/QS.svg" alt="Queen" className="w-10 h-14 mr-2" />
//               <span>Queen = 2 points</span>
//             </div>
//           </div>
//           <p className="font-bold">The most important rule is to follow suit, if a player doesn't have that suit, he/she may play any other card in his deck.</p>
//         </>
//       )
//     },
//     {
//       title: "IMPORTANT FEATURES",
//       content: (
//         <>
//           <ul className="list-disc pl-5 space-y-2">
//             <li><strong>Keeping Trump:</strong> The person to the right of the dealer must select trump from his/her first 4 cards which will be the highest card of the suit he/she has the most of.</li>
//             <li><strong>Calling to trump:</strong> Calling is done midway through dealing when you receive your first 4 cards. Calling is done if you have a favourable hand and would like to trump. The thinking is that you can hold off the opposition team with your hand. The call which is generally increments of 10 is subtracted from the counting score. The maximum call is 104.</li>
//             <li><strong>Trumpless:</strong> Both teams are to have at least 1 trump. If not would lead to a re-deal. The responsibility to call "no trump" is up to the counting team.</li>
//             <li><strong>Chopping:</strong> A hand can be chopped (cut) if a player doesn't have the suit of the first card on the table. If you have the suit and decide to chop the hand and if you are caught you will lose 4 game points (4balled).</li>
//           </ul>
//         </>
//       )
//     },
//     {
//       title: "SPECIAL CALLS",
//       content: (
//         <>
//           <ul className="list-disc pl-5 space-y-2">
//             <li><strong>Thunee:</strong> After receiving the 6 cards one can call "Thunee". When Thunee is called the first card played is trump and one has to win all 6 hands to gain 4 game points. If you don't you will lose 4 points (4 balled). If you were in a "calling" and won it, you as the Trumper get the first choice to call Thunee.</li>
//             <li><strong>Jodhi:</strong> Jodhi cards are the Queen, King and Jack of the same suit. You and your partner can call Jodhi upon winning your first and third hand. Both teams' Jodhi's are offset, and the balance is used on the 105 counts.</li>
//             <li><strong>Jodhi Combinations (non-trump):</strong> Queen and King = 20. Queen, King, and Jack = 30.</li>
//             <li><strong>Jodhi Combinations (trump):</strong> Queen and King = 40. Queen, King, and Jack = 50.</li>
//             <li><strong>Khanuck:</strong> Only directly related to Jodhi. You can call a Khanuck in the last hand if you are going to win that last hand and you believe that your opponent took a lower score than your Jodhi. If your Jodhi was 30 + (10 for taking the last hand) and your opponent took hands that have a total score less than your 40, then your Khanuck call is correct and you are awarded three balls.</li>
//             <li><strong>Double/Two:</strong> When you have taken the first five hands and believe you will win the last hand you can call a double/two. Only the controlling card holder that secures the last hand can call double/two on the last hand. If you successfully win the last hand (all 6 hands) you will be awarded 2 balls after calling double/two.</li>
//           </ul>
//         </>
//       )
//     },
//     {
//       title: "DETERMINING THE DEALER",
//       content: (
//         <>
//           <p>To select the initial dealer, the deck is shuffled and dealt face-up, one card at a time, in a counter-clockwise direction. The first player to receive a black Jack (either the Jack of Clubs or Jack of Spades) becomes the dealer for the first round.</p>
//           <div className="flex items-center space-x-4 my-4">
//             <div className="flex flex-col items-center">
//               <img src="/CardFace/JC.svg" alt="Jack of Clubs" className="w-16 h-24" />
//               <span className="text-sm mt-1">Jack of Clubs</span>
//             </div>
//             <div className="flex flex-col items-center">
//               <img src="/CardFace/JS.svg" alt="Jack of Spades" className="w-16 h-24" />
//               <span className="text-sm mt-1">Jack of Spades</span>
//             </div>
//           </div>
//           <p>In subsequent rounds, the deal passes to the player on the dealer's right.</p>
//         </>
//       )
//     },
//     {
//       title: "CUTTING & DEALING",
//       content: (
//         <>
//           <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Cutting the Deck</h4>
//           <p>Before dealing, the dealer offers the deck to the player on their immediate left for cutting. The player may choose to cut the deck or decline. If the deck is passed over the table, a cut is mandatory; otherwise, it's optional.</p>

//           <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Dealing the Cards</h4>
//           <p>The dealer distributes the cards in a counter-clockwise direction, starting with the player to their right. Each player initially receives four cards.</p>
//           <p>After the bidding phase (where players bid to determine the trump suit), the dealer deals two additional cards to each player, bringing their hand to six cards.</p>

//           <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Important Rules</h4>
//           <ul className="list-disc pl-5 space-y-2">
//             <li><strong>Trump selection:</strong> The trump person must select trump from his/her first 4 cards before his/her partner receives their first 4 cards.</li>
//             <li><strong>Trump reveal:</strong> Once the first card is played, the trump man will show the trump card (trump suit) to all players.</li>
//             <li><strong>Deal change:</strong> The deal will only change once the dealing team reaches or passes the opposing teams' points on the ball cards.</li>
//             <li><strong>No riffling:</strong> No riffling of the cards is allowed, and the dealer may not look at the bottom card.</li>
//           </ul>
//         </>
//       )
//     },
//     {
//       title: "TRICK-TAKING RULES",
//       content: (
//         <>
//           <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Playing Tricks</h4>
//           <p>After all players have received their 6 cards, the gameplay begins:</p>
//           <ul className="list-disc pl-5 space-y-2">
//             <li>The player to the dealer's left leads the first trick by playing any card.</li>
//             <li>Play proceeds counter-clockwise, with each player playing one card.</li>
//             <li><strong>Following suit is mandatory</strong> - if you have a card of the suit that was led, you must play it.</li>
//             <li>If you don't have the led suit, you may play any card, including a trump.</li>
//             <li>The highest card of the led suit wins the trick, unless a trump is played.</li>
//             <li>If any trumps are played, the highest trump wins the trick.</li>
//             <li>The winner of each trick leads the next one.</li>
//           </ul>

//           <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Chopping (Cutting)</h4>
//           <p>A hand can be chopped (cut) if a player doesn't have the suit of the first card on the table. If you have the suit and decide to chop the hand and if you are caught, your team will lose 4 game points (4-balled).</p>
//         </>
//       )
//     },
//     {
//       title: "POINTS & PENALTIES",
//       content: (
//         <>
//           <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Special Call Points</h4>
//           <ul className="list-disc pl-5 space-y-2">
//             <li><strong>Thunee:</strong> 4 points for a win and 4 points for a loss to the opposing team.</li>
//             <li><strong>Double:</strong> 2 points and a wrong claim is 4 points to the opposing team.</li>
//             <li><strong>Khanuck:</strong> 3 points and a wrong claim is 4 points to the opposing team.</li>
//           </ul>

//           <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">4-Ball Penalties</h4>
//           <p>The following actions result in a 4-ball penalty (4 game points awarded to the opposing team):</p>
//           <ul className="list-disc pl-5 space-y-1">
//             <li>Playing the wrong color when you have the correct suit</li>
//             <li>Cutting when you have the led suit</li>
//             <li>Playing out of turn (wrong come down)</li>
//             <li>Making a wrong claim to a hand</li>
//             <li>Telling your partner to open a ball</li>
//             <li>Calling double on a corner house (12 ball for a 13 ball win)</li>
//             <li>Looking at other player's cards or showing your cards</li>
//             <li>Riffling the cards or looking at the bottom card while dealing</li>
//             <li>Incorrect Jodhi call</li>
//           </ul>
//         </>
//       )
//     }
//   ];

//   return (
//     <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4 overflow-y-auto">
//       <div className="bg-black border-2 border-[#E1C760] rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
//         <div className="sticky top-0 bg-black border-b border-[#E1C760]/50 p-4 flex justify-between items-center">
//           <h2 className="text-2xl font-bold text-[#E1C760]">Thunee Game Rules</h2>
//           <Button
//             variant="ghost"
//             size="icon"
//             className="text-[#E1C760] hover:bg-[#E1C760]/10"
//             onClick={onClose}
//           >
//             <X className="h-6 w-6" />
//           </Button>
//         </div>

//         <div className="p-6">
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
//             <div className="md:col-span-1 space-y-2">
//               {sections.map((section, index) => (
//                 <Button
//                   key={index}
//                   variant={currentSection === index ? "default" : "outline"}
//                   className={`w-full justify-start ${
//                     currentSection === index
//                       ? "bg-[#E1C760] text-black"
//                       : "border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
//                   }`}
//                   onClick={() => setCurrentSection(index)}
//                 >
//                   {section.title}
//                 </Button>
//               ))}
//             </div>

//             <div className="md:col-span-2 bg-black/50 border border-[#E1C760]/30 rounded-lg p-4">
//               <h3 className="text-xl font-bold text-[#E1C760] mb-4">{sections[currentSection].title}</h3>
//               <div className="text-white space-y-4">
//                 {sections[currentSection].content}
//               </div>
//             </div>
//           </div>

//           <div className="flex justify-between mt-6">
//             <Button
//               variant="outline"
//               className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
//               onClick={() => setCurrentSection(Math.max(0, currentSection - 1))}
//               disabled={currentSection === 0}
//             >
//               Previous
//             </Button>

//             <Button
//               variant="outline"
//               className="border-[#E1C760] text-[#E1C760] hover:bg-[#E1C760]/10"
//               onClick={() => setCurrentSection(Math.min(sections.length - 1, currentSection + 1))}
//               disabled={currentSection === sections.length - 1}
//             >
//               Next
//             </Button>
//           </div>

//           <div className="mt-6 text-center">
//             <Button
//               variant="default"
//               className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80"
//               onClick={onContinue || onClose}
//             >
//               I Understand the Rules
//             </Button>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface GameRulesProps {
  onClose: () => void;
  onContinue?: () => void;
}

export default function GameRules({ onClose, onContinue }: GameRulesProps) {
  const [currentSection, setCurrentSection] = useState(0);

  const sections = [
    {
      title: "HOW THE GAME IS PLAYED",
      content: (
        <>
          <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Game Overview</h4>
          <p>Thunee is a trick-taking card game played with 4 people, divided into two teams of 2 players each.</p>
          <p>Your partner sits opposite you at the table and your opponents are to the left and right of you.</p>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Team Roles</h4>
          <p>There are two main roles in the game:</p>
          <ul className="list-disc pl-5 space-y-1 mb-3">
            <li><strong>Scoring Team:</strong> Your aim is to win 105 points worth of cards.</li>
            <li><strong>Trumping Team:</strong> Your aim is to stop your opponents from counting to 105 points.</li>
          </ul>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Game Flow</h4>
          <ol className="list-decimal pl-5 space-y-1">
            <li>Determine the dealer (first player to receive a black Jack)</li>
            <li>The dealer offers the deck to the player on their left to cut</li>
            <li>The dealer distributes 4 cards to each player</li>
            <li>The player to the dealer's right selects the trump suit</li>
            <li>Bidding phase occurs (optional)</li>
            <li>The dealer distributes 2 more cards to each player</li>
            <li>Players play tricks, following suit when possible</li>
            <li>After all 6 tricks are played, points are tallied</li>
            <li>If the scoring team reaches 105+ points, they win a ball; otherwise, the trumping team wins</li>
          </ol>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Winning the Game</h4>
          <p>By achieving your team's aim, you will be awarded a 'ball' (game point). The first team to win 13 balls wins the game.</p>
        </>
      )
    },
    {
      title: "POINT VALUES OF THE CARDS",
      content: (
        <>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                             <img src="/CardFace/JS.svg" alt="Jack" className="w-10 h-14 mr-2" />

              </div>
              <span>Jack = 30 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
                           <img src="/CardFace/9S.svg" alt="Jack" className="w-10 h-14 mr-2" />

              </div>
              <span>Nine = 20 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
              <img src="/CardFace/AS.svg" alt="Jack" className="w-10 h-14 mr-2" />

              </div>
              <span>Ace = 11 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">1
              <img src="/CardFace/TS.svg" alt="Jack" className="w-10 h-14 mr-2" />

              </div>
              <span>Ten = 10 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
              <img src="/CardFace/KS.svg" alt="Jack" className="w-10 h-14 mr-2" />
              </div>
              <span>King = 3 points</span>
            </div>
            <div className="flex items-center">
              <div className="w-10 h-14 mr-2 bg-white rounded border flex items-center justify-center text-black font-bold">
              <img src="/CardFace/QS.svg" alt="Jack" className="w-10 h-14 mr-2" />
              </div>
              <span>Queen = 2 points</span>
            </div>
          </div>
          <p className="font-bold">The most important rule is to follow suit, if a player doesn't have that suit, he/she may play any other card in his deck.</p>
        </>
      )
    },
    {
      title: "IMPORTANT FEATURES",
      content: (
        <>
          <ul className="list-disc pl-5 space-y-2">
            <li><strong>Keeping Trump:</strong> The person to the right of the dealer must select trump from his/her first 4 cards which will be the highest card of the suit he/she has the most of.</li>
            <li><strong>Calling to trump:</strong> Calling is done midway through dealing when you receive your first 4 cards. Calling is done if you have a favourable hand and would like to trump. The thinking is that you can hold off the opposition team with your hand. The call which is generally increments of 10 is subtracted from the counting score. The maximum call is 104.</li>
            <li><strong>Trumpless:</strong> Both teams are to have at least 1 trump. If not would lead to a re-deal. The responsibility to call "no trump" is up to the counting team.</li>
            <li><strong>Chopping:</strong> A hand can be chopped (cut) if a player doesn't have the suit of the first card on the table. If you have the suit and decide to chop the hand and if you are caught you will lose 4 game points (4balled).</li>
          </ul>
        </>
      )
    },
    {
      title: "SPECIAL CALLS",
      content: (
        <>
          <ul className="list-disc pl-5 space-y-2">
            <li><strong>Thunee:</strong> After receiving the 6 cards one can call "Thunee". When Thunee is called the first card played is trump and one has to win all 6 hands to gain 4 game points. If you don't you will lose 4 points (4 balled). If you were in a "calling" and won it, you as the Trumper get the first choice to call Thunee.</li>
            <li><strong>Jodhi:</strong> Jodhi cards are the Queen, King and Jack of the same suit. You and your partner can call Jodhi upon winning your first and third hand. Both teams' Jodhi's are offset, and the balance is used on the 105 counts.</li>
            <li><strong>Jodhi Combinations (non-trump):</strong> Queen and King = 20. Queen, King, and Jack = 30.</li>
            <li><strong>Jodhi Combinations (trump):</strong> Queen and King = 40. Queen, King, and Jack = 50.</li>
            <li><strong>Khanuck:</strong> Only directly related to Jodhi. You can call a Khanuck in the last hand if you are going to win that last hand and you believe that your opponent took a lower score than your Jodhi. If your Jodhi was 30 + (10 for taking the last hand) and your opponent took hands that have a total score less than your 40, then your Khanuck call is correct and you are awarded three balls.</li>
            <li><strong>Double/Two:</strong> When you have taken the first five hands and believe you will win the last hand you can call a double/two. Only the controlling card holder that secures the last hand can call double/two on the last hand. If you successfully win the last hand (all 6 hands) you will be awarded 2 balls after calling double/two.</li>
          </ul>
        </>
      )
    },
    {
      title: "DETERMINING THE DEALER",
      content: (
        <>
          <p>To select the initial dealer, the deck is shuffled and dealt face-up, one card at a time, in a counter-clockwise direction. The first player to receive a black Jack (either the Jack of Clubs or Jack of Spades) becomes the dealer for the first round.</p>
          <div className="flex items-center space-x-4 my-4">
            <div className="flex flex-col items-center">
              <div className="w-16 h-24 bg-white rounded border flex items-center justify-center text-black font-bold text-lg">
              <img src="/CardFace/JC.svg" alt="Jack" className="w-10 h-14 mr-2" />
              </div>
              <span className="text-sm mt-1">Jack of Clubs</span>
            </div>
            <div className="flex flex-col items-center">
              <div className="w-16 h-24 bg-white rounded border flex items-center justify-center text-black font-bold text-lg">
              <img src="/CardFace/JS.svg" alt="Jack" className="w-10 h-14 mr-2" />
              </div>
              <span className="text-sm mt-1">Jack of Spades</span>
            </div>
          </div>
          <p>In subsequent rounds, the deal passes to the player on the dealer's right.</p>
        </>
      )
    },
    {
      title: "CUTTING & DEALING",
      content: (
        <>
          <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Cutting the Deck</h4>
          <p>Before dealing, the dealer offers the deck to the player on their immediate left for cutting. The player may choose to cut the deck or decline. If the deck is passed over the table, a cut is mandatory; otherwise, it's optional.</p>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Dealing the Cards</h4>
          <p>The dealer distributes the cards in a counter-clockwise direction, starting with the player to their right. Each player initially receives four cards.</p>
          <p>After the bidding phase (where players bid to determine the trump suit), the dealer deals two additional cards to each player, bringing their hand to six cards.</p>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Important Rules</h4>
          <ul className="list-disc pl-5 space-y-2">
            <li><strong>Trump selection:</strong> The trump person must select trump from his/her first 4 cards before his/her partner receives their first 4 cards.</li>
            <li><strong>Trump reveal:</strong> Once the first card is played, the trump man will show the trump card (trump suit) to all players.</li>
            <li><strong>Deal change:</strong> The deal will only change once the dealing team reaches or passes the opposing teams' points on the ball cards.</li>
            <li><strong>No riffling:</strong> No riffling of the cards is allowed, and the dealer may not look at the bottom card.</li>
          </ul>
        </>
      )
    },
    {
      title: "TRICK-TAKING RULES",
      content: (
        <>
          <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Playing Tricks</h4>
          <p>After all players have received their 6 cards, the gameplay begins:</p>
          <ul className="list-disc pl-5 space-y-2">
            <li>The player to the dealer's left leads the first trick by playing any card.</li>
            <li>Play proceeds counter-clockwise, with each player playing one card.</li>
            <li><strong>Following suit is mandatory</strong> - if you have a card of the suit that was led, you must play it.</li>
            <li>If you don't have the led suit, you may play any card, including a trump.</li>
            <li>The highest card of the led suit wins the trick, unless a trump is played.</li>
            <li>If any trumps are played, the highest trump wins the trick.</li>
            <li>The winner of each trick leads the next one.</li>
          </ul>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">Chopping (Cutting)</h4>
          <p>A hand can be chopped (cut) if a player doesn't have the suit of the first card on the table. If you have the suit and decide to chop the hand and if you are caught, your team will lose 4 game points (4-balled).</p>
        </>
      )
    },
    {
      title: "POINTS & PENALTIES",
      content: (
        <>
          <h4 className="text-lg font-semibold text-[#E1C760] mb-2">Special Call Points</h4>
          <ul className="list-disc pl-5 space-y-2">
            <li><strong>Thunee:</strong> 4 points for a win and 4 points for a loss to the opposing team.</li>
            <li><strong>Double:</strong> 2 points and a wrong claim is 4 points to the opposing team.</li>
            <li><strong>Khanuck:</strong> 3 points and a wrong claim is 4 points to the opposing team.</li>
          </ul>

          <h4 className="text-lg font-semibold text-[#E1C760] mt-4 mb-2">4-Ball Penalties</h4>
          <p>The following actions result in a 4-ball penalty (4 game points awarded to the opposing team):</p>
          <ul className="list-disc pl-5 space-y-1">
            <li>Playing the wrong color when you have the correct suit</li>
            <li>Cutting when you have the led suit</li>
            <li>Playing out of turn (wrong come down)</li>
            <li>Making a wrong claim to a hand</li>
            <li>Telling your partner to open a ball</li>
            <li>Calling double on a corner house (12 ball for a 13 ball win)</li>
            <li>Looking at other player's cards or showing your cards</li>
            <li>Riffling the cards or looking at the bottom card while dealing</li>
            <li>Incorrect Jodhi call</li>
          </ul>
        </>
      )
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4 overflow-y-auto">
      <div className="bg-black border-2 border-[#E1C760] rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-black border-b border-[#E1C760]/50 p-4 flex justify-between items-center">
          <h2 className="text-2xl font-bold text-[#E1C760]">Thunee Game Rules</h2>
       
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="md:col-span-1 space-y-2">
              {sections.map((section, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className={`w-full justify-start border-[#E1C760] transition-colors ${
                    currentSection === index
                      ? "bg-[#E1C760] text-black hover:bg-[#E1C760]/90 hover:text-black"
                      : "bg-black text-[#E1C760] hover:bg-[#E1C760] hover:text-black"
                  }`}
                  onClick={() => setCurrentSection(index)}
                >
                  {section.title}
                </Button>
              ))}
            </div>

            <div className="md:col-span-2 bg-black/50 border border-[#E1C760]/30 rounded-lg p-4">
              <h3 className="text-xl font-bold text-[#E1C760] mb-4">{sections[currentSection].title}</h3>
              <div className="text-white space-y-4">
                {sections[currentSection].content}
              </div>
            </div>
          </div>

          <div className="flex justify-between mt-6">
            <Button
              variant="outline"
              className="border-[#E1C760] bg-black text-[#E1C760] hover:bg-[#E1C760] hover:text-black transition-colors"
              onClick={() => setCurrentSection(Math.max(0, currentSection - 1))}
              disabled={currentSection === 0}
            >
              Previous
            </Button>

            <Button
              variant="outline"
              className="border-[#E1C760] bg-black text-[#E1C760] hover:bg-[#E1C760] hover:text-black transition-colors"
              onClick={() => setCurrentSection(Math.min(sections.length - 1, currentSection + 1))}
              disabled={currentSection === sections.length - 1}
            >
              Next
            </Button>
          </div>

          <div className="mt-6 text-center">
            <Button
              variant="outline"
              className="bg-[#E1C760] text-black border-[#E1C760] hover:bg-[#E1C760]/90 hover:text-black transition-colors"
              onClick={onContinue || onClose}
            >
              I Understand the Rules
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}