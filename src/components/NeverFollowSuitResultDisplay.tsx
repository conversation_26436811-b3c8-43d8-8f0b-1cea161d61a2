"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore } from "@/store/gameStore";
import { CheckCircle, XCircle, ChevronDown, ChevronUp } from "lucide-react";
import { getCardImagePath } from "@/utils/cardUtils";
import "../styles/FourBallResultDisplay.css";

type NeverFollowSuitResultProps = {
  isOpen: boolean;
  onClose: () => void;
  result: {
    ballType: string;
    option: string;
    targetPlayer: string;
    targetPlayerName: string;
    targetTeam: 1 | 2;
    accuserId: string;
    accuserName: string;
    accuserTeam: 1 | 2;
    handNumber: number;
    leadSuit: string;
    cardPlayed: {
      suit: string;
      value: string;
    };
    handBeforePlaying: Array<{
      suit: string;
      value: string;
    }>;
    // All cards played in the selected hand by all players
    selectedHandCards?: Array<{
      suit: string;
      value: string;
      playedBy: string;
      playerName: string;
      playerTeam: 1 | 2;
    }>;
    isValid: boolean;
    followedSuit: boolean;
    hadLeadSuit: boolean;
    winningTeam: 1 | 2;
    ballsAwarded: number;
  } | null;
};

export default function NeverFollowSuitResultDisplay({
  isOpen,
  onClose,
  result
}: NeverFollowSuitResultProps) {
  const { teamNames } = useGameStore();
  const [countdown, setCountdown] = useState(10);
  const [showHandCards, setShowHandCards] = useState(false);
  const [showSelectedHand, setShowSelectedHand] = useState(false);

  // Auto-close after countdown
  useEffect(() => {
    if (!isOpen || !result) return;

    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          onClose();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, [isOpen, onClose, result]);

  // Reset countdown when modal opens
  useEffect(() => {
    if (isOpen) {
      setCountdown(10);
      setShowHandCards(false);
      setShowSelectedHand(false);
    }
  }, [isOpen]);

  if (!isOpen || !result) return null;

  // Function to format suit name
  const formatSuit = (suit: string | undefined) => {
    if (!suit) return 'Unknown';
    return suit.charAt(0).toUpperCase() + suit.slice(1);
  };

  return (
    <AnimatePresence>
      {isOpen && result && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="four-ball-container"
        >
          <div className="four-ball-backdrop" />
          <div className="four-ball-card">
            <div className="four-ball-header">
              <h2 className="four-ball-title">4-Ball Result</h2>
              <p className="four-ball-subtitle">
                {result.option} by {result.accuserName} ({teamNames[result.accuserTeam]})
              </p>
            </div>

            <div className="four-ball-content">
              <div className="four-ball-section">
                <h3 className="four-ball-section-title">Accusation Details</h3>
                <p className="four-ball-text">
                  Player: <span className="four-ball-text-bold">{result.targetPlayerName}</span> ({teamNames[result.targetTeam]})
                </p>
                <p className="four-ball-text">
                  Hand #: <span className="four-ball-text-bold">{result.handNumber}</span>
                </p>
                <p className="four-ball-text">
                  Lead Suit: <span className="four-ball-text-bold">{formatSuit(result.leadSuit)}</span>
                </p>
                <p className="four-ball-text">
                  Card Played: <span className="four-ball-text-bold">
                    {result.cardPlayed?.value || 'Unknown'} of {formatSuit(result.cardPlayed?.suit)}
                  </span>
                </p>
                <div className="four-ball-status">
                  <p className="four-ball-status-text">Status:</p>
                  {result.isValid ? (
                    <div className="four-ball-status-valid">
                      <CheckCircle className="four-ball-status-icon" />
                      <span>Valid Accusation</span>
                    </div>
                  ) : (
                    <div className="four-ball-status-invalid">
                      <XCircle className="four-ball-status-icon" />
                      <span>Invalid Accusation</span>
                    </div>
                  )}
                </div>

                {/* Clickable hand number to show cards */}
                <div
                  className="four-ball-text four-ball-text-bold mt-3 text-center cursor-pointer"
                  style={{ color: '#E1C760' }}
                  onClick={() => setShowHandCards(!showHandCards)}
                >
                  {showHandCards ? "Hide Hand Cards" : "Click to View Hand Cards"}
                </div>
              </div>

              {/* Hand cards section - toggleable */}
              <div className={`four-ball-hand-details ${showHandCards ? 'visible' : ''}`}>
                <h3 className="four-ball-hand-title">Player's Hand</h3>
                {result.handBeforePlaying && result.handBeforePlaying.length > 0 ? (
                  <>
                    <p className="four-ball-text mb-2">
                      All cards in player's hand:
                    </p>
                    <div className="four-ball-hand-cards">
                      {result.handBeforePlaying.map((card, index) => (
                        <div
                          key={index}
                          className={`four-ball-card-wrapper ${card.suit === result.leadSuit ? 'four-ball-card-highlight' : ''}`}
                        >
                          <img
                            src={getCardImagePath(card.value, card.suit)}
                            alt={`${card.value} of ${card.suit}`}
                          />
                        </div>
                      ))}
                    </div>
                    {result.isValid && (
                      <p className="four-ball-text mt-2 text-center" style={{ color: '#10b981' }}>
                        Player had {result.handBeforePlaying.filter(card => card.suit === result.leadSuit).length} cards of the lead suit but played {result.cardPlayed?.value || 'Unknown'} of {formatSuit(result.cardPlayed?.suit)}
                      </p>
                    )}
                  </>
                ) : (
                  <p className="four-ball-text mt-2 text-center text-yellow-400">
                    Player's hand details not available
                  </p>
                )}
              </div>

              {/* Selected Hand Display - toggleable */}
              <div className="four-ball-section">
                <div
                  className="four-ball-section-title flex justify-between items-center cursor-pointer"
                  onClick={() => setShowSelectedHand(!showSelectedHand)}
                >
                  <h3>Selected Hand #{result.handNumber}</h3>
                  {showSelectedHand ? (
                    <ChevronUp className="h-5 w-5 text-[#E1C760]" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-[#E1C760]" />
                  )}
                </div>

                {showSelectedHand && result.selectedHandCards && result.selectedHandCards.length > 0 && (
                  <div className="mt-2">
                    <p className="four-ball-text mb-2">
                      Cards played in this hand:
                    </p>
                    <div className="four-ball-hand-cards">
                      {result.selectedHandCards.map((card, index) => (
                        <div
                          key={index}
                          className={`four-ball-card-wrapper ${
                            card.suit === result.leadSuit ? 'four-ball-card-highlight' : ''
                          } ${card.playedBy === result.targetPlayer ? 'four-ball-card-accused' : ''}`}
                        >
                          <img
                            src={getCardImagePath(card.value, card.suit)}
                            alt={`${card.value} of ${card.suit}`}
                          />
                          <div className="four-ball-card-player">
                            {card.playerName}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {showSelectedHand && (!result.selectedHandCards || result.selectedHandCards.length === 0) && (
                  <p className="four-ball-text mt-2 text-center text-yellow-400">
                    Hand details not available
                  </p>
                )}
              </div>

              <div className="four-ball-section">
                <h3 className="four-ball-section-title">Result</h3>
                <p className="four-ball-text four-ball-text-bold" style={{ textAlign: 'center' }}>
                  {teamNames[result.winningTeam]} wins {result.ballsAwarded} balls
                </p>
                <p className="four-ball-text mt-2">
                  {result.isValid ? (
                    `${result.targetPlayerName} did not follow suit when they could have. ${teamNames[result.accuserTeam]} wins 4 balls.`
                  ) : result.followedSuit ? (
                    `${result.targetPlayerName} correctly followed suit with ${result.cardPlayed?.value || 'Unknown'} of ${formatSuit(result.cardPlayed?.suit)}. ${teamNames[result.targetTeam]} wins 4 balls.`
                  ) : (
                    `${result.targetPlayerName} could not follow suit because they had no ${formatSuit(result.leadSuit)} cards. ${teamNames[result.targetTeam]} wins 4 balls.`
                  )}
                </p>
              </div>
            </div>

            <div className="four-ball-footer">
              <button
                onClick={onClose}
                className="four-ball-button"
              >
                Continue ({countdown})
              </button>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
