import { useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import socketService from "@/services/socketService";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface GameControlsProps {
  onShowRules?: () => void;
  disabled?: boolean;
}

const JordhiOptions = ["20", "20", "30", "40", "50"];
const callOptions = ["Doubles", "Khanak", "Thunee"];
const ballOptions = ["1 ball", "2 ball", "3 ball", "4 ball", "8 ball"];
const fouroreightBallOptions = ["4 ball", "8 ball"];

const nestedOptions: Record<string, string[]> = {
  "4 ball": ["Under chopped", "Never follow suit", "Called incorrect Jodhi", "Partner caught you"],
  "8 ball": ["Under chopped", "Never follow suit", "Called incorrect Jodhi", "Partner caught you"],
};

interface FourEightBallButtonProps {
  option: string;
  containerClasses: string;
  onClose: () => void;
}

const FourEightBallButton = ({
  option,
  containerClasses,
  onClose,
}: FourEightBallButtonProps) => {
  const [openNested, setOpenNested] = useState(false);

  return (
    <div className="relative w-full">
      <Popover open={openNested} onOpenChange={setOpenNested}>
        <div className="relative">
          <PopoverContent
            className="p-0 bg-transparent border-0 shadow-none w-24 absolute z-50 bottom-full left-1/2 -translate-x-1/2"
            sideOffset={0}
            align="center"
          >
            <motion.div
              initial={{ opacity: 0, scaleY: 0, transformOrigin: "bottom" }}
              animate={{ opacity: 1, scaleY: 1 }}
              exit={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.15 }}
              className={cn(containerClasses, "absolute bottom-0")}
            >
              <div className="flex flex-col items-center space-y-2">
                {nestedOptions[option].map((nestedOption: string, index: number) => (
                  <button
                    key={index}
                    onClick={() => {
                      setOpenNested(false);
                      onClose();
                      console.log(`Selected ${option} with option: ${nestedOption}`);
                      socketService.sendGameAction("four_eight_ball_selection", {
                        ballType: option,
                        option: nestedOption
                      });
                    }}
                    className="w-full h-8 text-sm font-medium text-[#E1C760] bg-black rounded-lg border border-[#E1C760] hover:bg-[#E1C760]/10"
                  >
                    {nestedOption}
                  </button>
                ))}
              </div>
            </motion.div>
          </PopoverContent>
          <PopoverTrigger asChild>
            <button className="w-full h-8 text-sm font-medium text-[#E1C760] bg-black rounded-lg border border-[#E1C760] hover:bg-[#E1C760]/10 relative z-10">
              {option}
            </button>
          </PopoverTrigger>
        </div>
      </Popover>
    </div>
  );
};

export default function GameControls({
  onShowRules,
  disabled = false, // Default to enabled
}: GameControlsProps) {
  const [openJordhi, setOpenJordhi] = useState(false);
  const [openCall, setOpenCall] = useState(false);
  const [openBall, setOpenBall] = useState(false);
  const [openfouroreightBall, setOpenfouroreightBall] = useState(false);

  const containerClasses = cn(
    "p-2 rounded-lg bg-black/80 border border-[#E1C760]",
    "backdrop-blur-sm shadow-[0_0_5px_rgba(225,199,96,0.5)]",
    "transform origin-bottom w-full"
  );

  return (
    <div className="flex flex-col items-center gap-0 w-32">
      {/* Title */}
      <div className="w-full h-8 bg-black text-[#E1C760] text-sm font-medium rounded-t-lg border border-[#E1C760] flex items-center justify-center">
        Calls
      </div>

      <Popover open={openCall} onOpenChange={setOpenCall}>
        <div className="relative flex flex-col items-center w-full">
          <PopoverContent
            className="p-0 bg-transparent border-0 shadow-none w-full"
            sideOffset={0}
            align="center"
            side="top"
          >
            <motion.div
              initial={{ opacity: 0, scaleY: 0, transformOrigin: "bottom" }}
              animate={{ opacity: 1, scaleY: 1 }}
              exit={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.15 }}
              className={containerClasses}
            >
              <div className="flex flex-col items-center space-y-0">
                {callOptions.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setOpenCall(false);
                      // Handle the call option
                      if (option === "Thunee") {
                        console.log("Thunee called");
                        // Send the Thunee call to the server
                        socketService.sendGameAction("call_thunee", {});
                      } else if (option === "Doubles") {
                        console.log("Double called");
                        // Send the Double call to the server
                        socketService.sendGameAction("call_double", {});
                      } else if (option === "Khanuck") {
                        console.log("Khanuck called");
                        // Send the Khanuck call to the server
                        socketService.sendGameAction("call_khanuck", {});
                      }
                    }}
                    className="w-full h-8 text-sm font-medium text-[#E1C760] bg-black rounded-lg border border-[#E1C760] hover:bg-[#E1C760]/10"
                  >
                    {option}
                  </button>
                ))}
              </div>
            </motion.div>
          </PopoverContent>
          <PopoverTrigger asChild>
            <button
              className={`h-10 w-full text-sm font-medium border-x border-b ${
                disabled
                  ? "bg-black/50 text-[#E1C760]/50 border-[#E1C760]/50 cursor-not-allowed"
                  : "bg-black text-[#E1C760] border-[#E1C760] hover:bg-[#E1C760]/10 animate-pulse"
              }`}
              onClick={() => setOpenCall(true)}
              disabled={disabled}
            >
              Calls
            </button>
          </PopoverTrigger>
        </div>
      </Popover>

      <Popover open={openBall} onOpenChange={setOpenBall}>
        <div className="relative flex flex-col items-center w-full">
          <PopoverContent
            className="p-0 bg-transparent border-0 shadow-none w-24"
            sideOffset={0}
            align="center"
            side="top"
          >
            <motion.div
              initial={{ opacity: 0, scaleY: 0, transformOrigin: "bottom" }}
              animate={{ opacity: 1, scaleY: 1 }}
              exit={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.15 }}
              className={containerClasses}
            >
              <div className="flex flex-col items-center space-y-2">
                {ballOptions.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setOpenBall(false);
                      console.log(`Ball option selected: ${option}`);
                      socketService.sendGameAction("ball_selection", { ballOption: option });
                    }}
                    className="w-full h-8 text-sm font-medium text-[#E1C760] bg-black rounded-lg border border-[#E1C760] hover:bg-[#E1C760]/10"
                  >
                    {option}
                  </button>
                ))}
              </div>
            </motion.div>
          </PopoverContent>
          <PopoverTrigger asChild>
            <button
              className="h-10 w-full bg-black text-[#E1C760] text-sm font-medium border-x border-b border-[#E1C760] hover:bg-[#E1C760]/10"
              onClick={() => setOpenBall(true)}
              disabled={disabled}
            >
              Ball
            </button>
          </PopoverTrigger>
        </div>
      </Popover>

      <Popover open={openfouroreightBall} onOpenChange={setOpenfouroreightBall}>
        <div className="relative flex flex-col items-center w-full">
          <PopoverContent
            className="p-0 bg-transparent border-0 shadow-none w-24"
            sideOffset={0}
            align="center"
            side="top"
          >
            <motion.div
              initial={{ opacity: 0, scaleY: 0, transformOrigin: "bottom" }}
              animate={{ opacity: 1, scaleY: 1 }}
              exit={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.15 }}
              className={containerClasses}
            >
              <div className="flex flex-col items-center space-y-2">
                {fouroreightBallOptions.map((option, index) => (
                  <FourEightBallButton
                    key={index}
                    option={option}
                    containerClasses={containerClasses}
                    onClose={() => setOpenfouroreightBall(false)}
                  />
                ))}
              </div>
            </motion.div>
          </PopoverContent>
          <PopoverTrigger asChild>
            <button
              className="h-10 w-full bg-black text-[#E1C760] text-sm font-medium border-x border-b border-[#E1C760] hover:bg-[#E1C760]/10"
              onClick={() => setOpenfouroreightBall(true)}
              disabled={disabled}
            >
              4/8 Ball
            </button>
          </PopoverTrigger>
        </div>
      </Popover>

      <Popover open={openJordhi} onOpenChange={setOpenJordhi}>
        <div className="relative flex flex-col items-center w-full">
          <PopoverContent
            className="p-0 bg-transparent border-0 shadow-none w-24"
            sideOffset={0}
            align="center"
            side="top"
          >
            <motion.div
              initial={{ opacity: 0, scaleY: 0, transformOrigin: "bottom" }}
              animate={{ opacity: 1, scaleY: 1 }}
              exit={{ opacity: 0, scaleY: 0 }}
              transition={{ duration: 0.15 }}
              className={containerClasses}
            >
              <div className="flex flex-col items-center space-y-2">
                {JordhiOptions.map((option, index) => {
                  // For the second 20-point option, add a suffix to distinguish it
                  const displayText = option === "20" && index === 1 ? "20 (2nd suit)" : option;

                  return (
                    <button
                      key={index}
                      onClick={() => {
                        setOpenJordhi(false);
                        console.log(`Jordhi option selected: ${option}`);
                        socketService.sendGameAction("jordhi_selection", { jordhiOption: option });
                      }}
                      className="w-full h-8 text-sm font-medium text-[#E1C760] bg-black rounded-lg border border-[#E1C760] hover:bg-[#E1C760]/10"
                    >
                      {displayText}
                    </button>
                  );
                })}
              </div>
            </motion.div>
          </PopoverContent>
          <PopoverTrigger asChild>
            <button
              className="h-10 w-full bg-black text-[#E1C760] text-sm font-medium rounded-b-lg border-x border-b border-[#E1C760] hover:bg-[#E1C760]/10"
              onClick={() => setOpenJordhi(true)}
              disabled={disabled}
            >
              Jordhi
            </button>
          </PopoverTrigger>
        </div>
      </Popover>

      {/* Rules Button - Moved to a separate position */}
      <button
        className="h-10 w-full bg-[#E1C760] text-black text-sm font-medium rounded-lg border border-[#E1C760] mt-4"
        onClick={onShowRules}
      >
        Rules
      </button>
    </div>
  );
}
