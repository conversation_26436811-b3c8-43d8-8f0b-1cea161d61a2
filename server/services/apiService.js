const axios = require('axios');
const https = require('https');
require('dotenv').config();

class ApiService {
  constructor() {
    this.baseURL = process.env.API_BASE_URL || 'https://localhost:57229';
    this.timeout = parseInt(process.env.API_TIMEOUT) || 10000;
    
    // Create axios instance with default configuration
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
      // For development with self-signed certificates
      httpsAgent: process.env.NODE_ENV === 'development' ?
        new https.Agent({ rejectUnauthorized: false }) : undefined
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API] Request error:', error.message);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[API] ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error(`[API] Response error:`, {
          url: error.config?.url,
          status: error.response?.status,
          message: error.response?.data?.message || error.message
        });
        return Promise.reject(error);
      }
    );
  }

  // Set authorization token for authenticated requests
  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete this.client.defaults.headers.common['Authorization'];
    }
  }

  // Validate user token with the API
  async validateToken(token) {
    try {
      const response = await this.client.get('/api/Auth/validate', {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data;
    } catch (error) {
      console.error('[API] Token validation failed:', error.message);
      return null;
    }
  }

  // Get user information by token
  async getUserInfo(token) {
    try {
      console.log(`[API] GET /api/Auth/me`);
      console.log(`[API] Token: ${token ? token.substring(0, 20) + '...' : 'null'}`);

      const response = await this.client.get('/api/Auth/me', {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log(`[API] Response status: ${response.status}`);
      console.log(`[API] Response data:`, response.data);

      return response.data.data;
    } catch (error) {
      console.log(`[API] Response error: {`);
      console.log(`  url: '/api/Auth/me',`);
      console.log(`  status: ${error.response?.status || 'unknown'},`);
      console.log(`  message: '${error.message}'`);
      console.log(`}`);

      // Don't log 401 errors as they're expected for invalid/expired tokens
      if (error.response?.status === 401) {
        console.log('[API] Token validation failed - user needs to authenticate');
      } else {
        console.error('[API] Failed to get user info:', error.message);
      }
      return null;
    }
  }

  // Create a new game session
  async createGame(gameData, hostToken) {
    try {
      const response = await this.client.post('/api/Games', gameData, {
        headers: { Authorization: `Bearer ${hostToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to create game:', error.message);
      throw error;
    }
  }

  // Join an existing game
  async joinGame(lobbyCode, playerData, playerToken) {
    try {
      const response = await this.client.post(`/api/Games/${lobbyCode}/join`, playerData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to join game:', error.message);
      throw error;
    }
  }

  // Start a game
  async startGame(lobbyCode, hostToken) {
    try {
      const response = await this.client.post(`/api/Games/${lobbyCode}/start`, {}, {
        headers: { Authorization: `Bearer ${hostToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to start game:', error.message);
      throw error;
    }
  }

  // Record a hand result
  async recordHandResult(lobbyCode, handData, playerToken) {
    try {
      const response = await this.client.post(`/api/Games/${lobbyCode}/hand-result`, handData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to record hand result:', error.message);
      // Don't throw error for hand recording to avoid disrupting gameplay
      return null;
    }
  }

  // Record a card play
  async recordCardPlay(cardPlayData, playerToken) {
    try {
      const response = await this.client.post('/api/Games/card-play', cardPlayData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to record card play:', error.message);
      // Don't throw error for card recording to avoid disrupting gameplay
      return null;
    }
  }

  // Record game result
  async recordGameResult(lobbyCode, gameResultData, playerToken) {
    try {
      const response = await this.client.post(`/api/Games/${lobbyCode}/game-result`, gameResultData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to record game result:', error.message);
      throw error;
    }
  }

  // Record Jordhi call
  async recordJordhiCall(jordhiData, playerToken) {
    try {
      const response = await this.client.post('/api/Games/jordhi-call', jordhiData, {
        headers: { Authorization: `Bearer ${playerToken}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to record Jordhi call:', error.message);
      // Don't throw error for Jordhi recording to avoid disrupting gameplay
      return null;
    }
  }

  // Health check
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      console.error('[API] Health check failed:', error.message);
      return null;
    }
  }

  // Game Settings methods
  async getGameSettings() {
    try {
      const response = await this.client.get('/api/gamesettings');
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get game settings:', error.message);
      // Return default settings if API call fails
      return {
        playTimeframeOptions: [3, 4, 5, 6, 60],
        defaultPlayTimeframe: 3,
        trumperThuneeCallingDuration: 5,
        firstRemainingThuneeCallingDuration: 3,
        lastRemainingThuneeCallingDuration: 2,
        votingTimeLimit: 15,
        trumpDisplayDuration: 10,
        cardDealingSpeed: 300,
        timerUpdateInterval: 100,
        cardFaceBaseUrl: '/CardFace',
        cardBackImageUrl: '/CardFace/card-back.svg',
        customCardFaceMappings: undefined
      };
    }
  }

  async updateGameSettings(settings) {
    try {
      const response = await this.client.put('/api/gamesettings', settings);
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to update game settings:', error.message);
      throw error;
    }
  }

  async resetGameSettings() {
    try {
      const response = await this.client.post('/api/gamesettings/reset');
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to reset game settings:', error.message);
      throw error;
    }
  }

  // Competition-related methods

  // Get all competitions
  async getCompetitions(token) {
    try {
      const response = await this.client.get('/api/competitions', {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competitions:', error.message);
      return null;
    }
  }

  // Get competition by ID
  async getCompetition(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition:', error.message);
      return null;
    }
  }

  // Create competition team
  async createCompetitionTeam(competitionId, teamData, token) {
    try {
      const response = await this.client.post(`/api/competitions/${competitionId}/teams/create`, teamData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to create competition team:', error.message);
      throw error;
    }
  }

  // Join competition team using invite code
  async joinCompetitionTeam(inviteCode, token) {
    try {
      const response = await this.client.post('/api/competitions/teams/join', { inviteCode }, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to join competition team:', error.message);
      throw error;
    }
  }

  // Get competition status for user
  async getCompetitionStatus(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/status`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition status:', error.message);
      return null;
    }
  }

  // Join a competition (legacy)
  async joinCompetition(competitionId, joinData, token) {
    try {
      const response = await this.client.post(`/api/competitions/${competitionId}/join`, joinData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to join competition:', error.message);
      throw error;
    }
  }

  // Get competition leaderboard
  async getCompetitionLeaderboard(competitionId, token, page = 1, pageSize = 20) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/leaderboard`, {
        headers: { Authorization: `Bearer ${token}` },
        params: { page, pageSize }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition leaderboard:', error.message);
      return null;
    }
  }

  // Get competition teams
  async getCompetitionTeams(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/teams`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to get competition teams:', error.message);
      return null;
    }
  }

  // Check if user is in competition
  async isUserInCompetition(competitionId, token) {
    try {
      const response = await this.client.get(`/api/competitions/${competitionId}/user-status`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to check user competition status:', error.message);
      return null;
    }
  }

  // Record competition game result (separate from regular games)
  async recordCompetitionGameResult(competitionGameData, token) {
    try {
      const response = await this.client.post('/api/competitions/games/result', competitionGameData, {
        headers: { Authorization: `Bearer ${token}` }
      });
      return response.data.data;
    } catch (error) {
      console.error('[API] Failed to record competition game result:', error.message);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new ApiService();
