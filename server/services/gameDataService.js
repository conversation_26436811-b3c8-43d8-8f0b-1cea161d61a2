const apiService = require('./apiService');
const authService = require('./authService');

class GameDataService {
  constructor() {
    // Track games that have been saved to API: lobbyCode -> gameId
    this.savedGames = new Map();
    
    // Queue for failed API calls to retry later
    this.retryQueue = [];
    
    // Start retry processor
    this.startRetryProcessor();
  }

  // Save game creation to API
  async saveGameCreation(lobby, hostSocketId) {
    try {
      const hostToken = authService.getUserToken(hostSocketId);
      if (!hostToken) {
        console.error('[GAME_DATA] No token found for host');
        return null;
      }

      const gameData = {
        lobbyCode: lobby.lobbyCode, // Pass the lobby code from Node.js server
        team1Name: lobby.teamNames?.[1] || 'Team 1',
        competitionId: lobby.competitionId || null,
        isCompetitionGame: !!lobby.competitionId,
        competitionGameNumber: lobby.competitionGameNumber || null
      };

      const savedGame = await apiService.createGame(gameData, hostToken);
      if (savedGame) {
        this.savedGames.set(lobby.lobbyCode, savedGame.id);
        console.log(`[GAME_DATA] Game created in API: ${lobby.lobbyCode} -> ${savedGame.id} ${lobby.competitionId ? `(Competition: ${lobby.competitionId})` : ''}`);
      }

      return savedGame;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save game creation:', error.message);
      this.addToRetryQueue('saveGameCreation', { lobby, hostSocketId });
      return null;
    }
  }

  // Save player joining game
  async savePlayerJoin(lobby, playerSocketId, playerName) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      const joinData = {
        playerName: playerName
      };

      const result = await apiService.joinGame(lobby.lobbyCode, joinData, playerToken);
      console.log(`[GAME_DATA] Player joined game in API: ${playerName} -> ${lobby.lobbyCode}`);
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save player join:', error.message);
      this.addToRetryQueue('savePlayerJoin', { lobby, playerSocketId, playerName });
      return null;
    }
  }

  // Save game start
  async saveGameStart(lobby, hostSocketId) {
    try {
      const hostToken = authService.getUserToken(hostSocketId);
      if (!hostToken) {
        console.error('[GAME_DATA] No token found for host');
        return null;
      }

      const result = await apiService.startGame(lobby.lobbyCode, hostToken);
      console.log(`[GAME_DATA] Game started in API: ${lobby.lobbyCode}`);
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save game start:', error.message);
      this.addToRetryQueue('saveGameStart', { lobby, hostSocketId });
      return null;
    }
  }

  // Save hand result
  async saveHandResult(lobby, handData, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      const handResultData = {
        lobbyCode: lobby.lobbyCode,
        handNumber: handData.handNumber || lobby.currentHand,
        ballNumber: handData.ballNumber || lobby.currentBall,
        winningPlayerId: handData.winner?.id,
        winningTeam: handData.winningTeam,
        points: handData.points || 0,
        leadSuit: handData.leadSuit,
        trumpSuit: lobby.gameState?.trumpSuit,
        playedCards: handData.cards?.map(card => ({
          playerId: card.playedBy,
          suit: card.suit,
          value: card.value,
          order: card.order || 0
        })) || []
      };

      const result = await apiService.recordHandResult(lobby.lobbyCode, handResultData, playerToken);
      console.log(`[GAME_DATA] Hand result saved: ${lobby.lobbyCode} Hand ${handResultData.handNumber}`);
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save hand result:', error.message);
      this.addToRetryQueue('saveHandResult', { lobby, handData, playerSocketId });
      return null;
    }
  }

  // Save card play
  async saveCardPlay(lobby, cardData, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        return null; // Silent fail for card plays to avoid spam
      }

      const cardPlayData = {
        lobbyCode: lobby.lobbyCode,
        handNumber: lobby.currentHand,
        ballNumber: lobby.currentBall,
        playerId: playerSocketId,
        suit: cardData.suit,
        value: cardData.value,
        playOrder: cardData.playOrder || 0,
        timestamp: new Date().toISOString()
      };

      const result = await apiService.recordCardPlay(cardPlayData, playerToken);
      return result;
    } catch (error) {
      // Silent fail for card plays to avoid disrupting gameplay
      this.addToRetryQueue('saveCardPlay', { lobby, cardData, playerSocketId });
      return null;
    }
  }

  // Save Jordhi call
  async saveJordhiCall(lobby, jordhiData, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      const jordhiCallData = {
        lobbyCode: lobby.lobbyCode,
        playerId: playerSocketId,
        playerName: jordhiData.playerName,
        playerTeam: jordhiData.playerTeam,
        value: jordhiData.value,
        handNumber: jordhiData.handNumber,
        ballNumber: lobby.currentBall,
        isValid: jordhiData.isFullyValid,
        jordhiSuit: jordhiData.jordhiSuit,
        timestamp: new Date().toISOString()
      };

      const result = await apiService.recordJordhiCall(jordhiCallData, playerToken);
      console.log(`[GAME_DATA] Jordhi call saved: ${jordhiData.playerName} called ${jordhiData.value}`);
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save Jordhi call:', error.message);
      this.addToRetryQueue('saveJordhiCall', { lobby, jordhiData, playerSocketId });
      return null;
    }
  }

  // Save game result (when game ends)
  async saveGameResult(lobby, gameResult, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      const gameResultData = {
        lobbyCode: lobby.lobbyCode,
        winningTeam: gameResult.winner,
        team1Score: gameResult.scores?.team1 || 0,
        team2Score: gameResult.scores?.team2 || 0,
        team1BallsWon: gameResult.ballScores?.team1 || 0,
        team2BallsWon: gameResult.ballScores?.team2 || 0,
        totalBalls: gameResult.totalBalls || 0,
        gameEndReason: gameResult.reason || 'completed',
        gameDurationMinutes: gameResult.duration || 0,
        endedAt: new Date().toISOString()
      };

      const result = await apiService.recordGameResult(lobby.lobbyCode, gameResultData, playerToken);
      console.log(`[GAME_DATA] Game result saved: ${lobby.lobbyCode} - Team ${gameResult.winner} won`);
      
      // Remove from saved games map
      this.savedGames.delete(lobby.lobbyCode);
      
      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save game result:', error.message);
      this.addToRetryQueue('saveGameResult', { lobby, gameResult, playerSocketId });
      return null;
    }
  }

  // Add failed operation to retry queue
  addToRetryQueue(operation, data) {
    this.retryQueue.push({
      operation,
      data,
      timestamp: new Date(),
      attempts: 0,
      maxAttempts: 3
    });
  }

  // Process retry queue
  async processRetryQueue() {
    if (this.retryQueue.length === 0) return;

    const now = new Date();
    const retryDelay = 30000; // 30 seconds

    for (let i = this.retryQueue.length - 1; i >= 0; i--) {
      const item = this.retryQueue[i];
      
      // Skip if not enough time has passed
      if (now - item.timestamp < retryDelay) continue;
      
      // Remove if max attempts reached
      if (item.attempts >= item.maxAttempts) {
        console.log(`[GAME_DATA] Giving up on retry: ${item.operation} after ${item.attempts} attempts`);
        this.retryQueue.splice(i, 1);
        continue;
      }

      // Attempt retry
      item.attempts++;
      console.log(`[GAME_DATA] Retrying operation: ${item.operation} (attempt ${item.attempts})`);
      
      try {
        let success = false;
        switch (item.operation) {
          case 'saveGameCreation':
            success = await this.saveGameCreation(item.data.lobby, item.data.hostSocketId);
            break;
          case 'savePlayerJoin':
            success = await this.savePlayerJoin(item.data.lobby, item.data.playerSocketId, item.data.playerName);
            break;
          case 'saveGameStart':
            success = await this.saveGameStart(item.data.lobby, item.data.hostSocketId);
            break;
          case 'saveHandResult':
            success = await this.saveHandResult(item.data.lobby, item.data.handData, item.data.playerSocketId);
            break;
          case 'saveJordhiCall':
            success = await this.saveJordhiCall(item.data.lobby, item.data.jordhiData, item.data.playerSocketId);
            break;
          case 'saveGameResult':
            success = await this.saveGameResult(item.data.lobby, item.data.gameResult, item.data.playerSocketId);
            break;
        }

        if (success) {
          console.log(`[GAME_DATA] Retry successful: ${item.operation}`);
          this.retryQueue.splice(i, 1);
        }
      } catch (error) {
        console.error(`[GAME_DATA] Retry failed: ${item.operation}`, error.message);
      }
    }
  }

  // Start retry processor (runs every 30 seconds)
  startRetryProcessor() {
    setInterval(() => {
      this.processRetryQueue();
    }, 30000);
  }

  // Save competition game result
  async saveCompetitionGameResult(lobby, gameResult, playerSocketId) {
    try {
      const playerToken = authService.getUserToken(playerSocketId);
      if (!playerToken) {
        console.error('[GAME_DATA] No token found for player');
        return null;
      }

      // Import competition utils for scoring calculation
      const competitionUtils = require('../utils/competitionUtils');
      const competitionService = require('./competitionService');

      const competitionGameData = competitionUtils.formatCompetitionGameResult(lobby, gameResult);

      // Use the competition-specific endpoint instead of regular game result endpoint
      const result = await apiService.recordCompetitionGameResult(competitionGameData, playerToken);

      if (result) {
        console.log(`[GAME_DATA] Competition game result saved: ${lobby.lobbyCode} in competition ${lobby.competitionId}`);
        console.log(`[GAME_DATA] Competition scoring: Team 1: ${competitionGameData.team1.points} points, Team 2: ${competitionGameData.team2.points} points`);

        // Update user game counts for both teams
        lobby.teams[1].forEach(player => {
          competitionService.updateUserGameCount(player.id, 1);
        });
        lobby.teams[2].forEach(player => {
          competitionService.updateUserGameCount(player.id, 1);
        });

        // Clear leaderboard cache to force refresh
        competitionService.clearCache();
      }

      return result;
    } catch (error) {
      console.error('[GAME_DATA] Failed to save competition game result:', error.message);
      this.addToRetryQueue('saveCompetitionGameResult', { lobby, gameResult, playerSocketId });
      return null;
    }
  }

  // Get statistics
  getStats() {
    return {
      savedGames: this.savedGames.size,
      retryQueueSize: this.retryQueue.length,
      retryQueue: this.retryQueue.map(item => ({
        operation: item.operation,
        attempts: item.attempts,
        timestamp: item.timestamp
      }))
    };
  }
}

// Export singleton instance
module.exports = new GameDataService();
