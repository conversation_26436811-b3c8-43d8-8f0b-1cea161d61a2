const gameDataService = require('../services/gameDataService');

/**
 * Save game result to API when game ends
 * @param {Object} lobby - The game lobby
 * @param {Object} gameEndData - Game end data
 * @param {Array} allPlayers - All players in the game
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveGameEndResult(lobby, gameEndData, allPlayers, lobbyCode) {
  try {
    // Find the host player to use their token for the API call
    const hostPlayer = allPlayers.find(p => p.isHost);
    if (!hostPlayer) {
      console.error(`[GAME_DATA] No host player found for game end in ${lobbyCode}`);
      return;
    }

    // Check if this is a competition game
    if (lobby.competitionId) {
      // Format game result for competition scoring
      const competitionGameResult = {
        winningTeam: gameEndData.winner,
        team1Score: gameEndData.finalScores?.team1 || gameEndData.scores?.team1 || 0,
        team2Score: gameEndData.finalScores?.team2 || gameEndData.scores?.team2 || 0,
        ballDifference: Math.abs((gameEndData.finalScores?.team1 || gameEndData.scores?.team1 || 0) - (gameEndData.finalScores?.team2 || gameEndData.scores?.team2 || 0)),
        gameEndReason: gameEndData.reason || 'completed',
        duration: gameEndData.gameHistory?.duration || gameEndData.duration || 0
      };

      // Save competition game result using competition-specific endpoint
      gameDataService.saveCompetitionGameResult(lobby, competitionGameResult, hostPlayer.id).catch(error => {
        console.error(`[GAME_DATA] Failed to save competition game result for ${lobbyCode}:`, error.message);
      });

      console.log(`[GAME_DATA] Competition game result save initiated for ${lobbyCode} in competition ${lobby.competitionId}`);
      console.log(`[GAME_DATA] Competition game scores: Team 1: ${competitionGameResult.team1Score}, Team 2: ${competitionGameResult.team2Score}, Winner: Team ${competitionGameResult.winningTeam}`);
    } else {
      // Save regular game result to API (async, don't wait for it)
      gameDataService.saveGameResult(lobby, gameEndData, hostPlayer.id).catch(error => {
        console.error(`[GAME_DATA] Failed to save game result for ${lobbyCode}:`, error.message);
      });

      console.log(`[GAME_DATA] Regular game result save initiated for ${lobbyCode}`);
    }
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating game result save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save hand result to API when hand completes
 * @param {Object} lobby - The game lobby
 * @param {Object} handData - Hand completion data
 * @param {string} winningPlayerId - ID of the winning player
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveHandEndResult(lobby, handData, winningPlayerId, lobbyCode) {
  try {
    // Save hand result to API (async, don't wait for it)
    gameDataService.saveHandResult(lobby, handData, winningPlayerId).catch(error => {
      console.error(`[GAME_DATA] Failed to save hand result for ${lobbyCode}:`, error.message);
    });

    console.log(`[GAME_DATA] Hand result save initiated for ${lobbyCode} hand ${handData.handId}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating hand result save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save card play to API
 * @param {Object} lobby - The game lobby
 * @param {Object} cardData - Card play data
 * @param {string} playerId - ID of the player who played the card
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveCardPlayResult(lobby, cardData, playerId, lobbyCode) {
  try {
    // Save card play to API (async, don't wait for it)
    gameDataService.saveCardPlay(lobby, cardData, playerId).catch(error => {
      // Silent fail for card plays to avoid disrupting gameplay
      console.error(`[GAME_DATA] Failed to save card play for ${lobbyCode}:`, error.message);
    });
  } catch (error) {
    // Silent fail for card plays
    console.error(`[GAME_DATA] Error initiating card play save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save Jordhi call to API
 * @param {Object} lobby - The game lobby
 * @param {Object} jordhiData - Jordhi call data
 * @param {string} playerId - ID of the player who made the call
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveJordhiCallResult(lobby, jordhiData, playerId, lobbyCode) {
  try {
    // Save Jordhi call to API (async, don't wait for it)
    gameDataService.saveJordhiCall(lobby, jordhiData, playerId).catch(error => {
      console.error(`[GAME_DATA] Failed to save Jordhi call for ${lobbyCode}:`, error.message);
    });

    console.log(`[GAME_DATA] Jordhi call save initiated for ${lobbyCode}: ${jordhiData.playerName} called ${jordhiData.value}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating Jordhi call save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save game start to API
 * @param {Object} lobby - The game lobby
 * @param {string} hostPlayerId - ID of the host player
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveGameStartResult(lobby, hostPlayerId, lobbyCode) {
  try {
    // Save game start to API (async, don't wait for it)
    gameDataService.saveGameStart(lobby, hostPlayerId).catch(error => {
      console.error(`[GAME_DATA] Failed to save game start for ${lobbyCode}:`, error.message);
    });

    console.log(`[GAME_DATA] Game start save initiated for ${lobbyCode}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating game start save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save player join to API
 * @param {Object} lobby - The game lobby
 * @param {string} playerId - ID of the joining player
 * @param {string} playerName - Name of the joining player
 * @param {string} lobbyCode - Lobby code for logging
 */
function savePlayerJoinResult(lobby, playerId, playerName, lobbyCode) {
  try {
    // Save player join to API (async, don't wait for it)
    gameDataService.savePlayerJoin(lobby, playerId, playerName).catch(error => {
      console.error(`[GAME_DATA] Failed to save player join for ${lobbyCode}:`, error.message);
    });

    console.log(`[GAME_DATA] Player join save initiated for ${lobbyCode}: ${playerName}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating player join save for ${lobbyCode}:`, error.message);
  }
}

/**
 * Save game creation to API
 * @param {Object} lobby - The game lobby
 * @param {string} hostPlayerId - ID of the host player
 * @param {string} lobbyCode - Lobby code for logging
 */
function saveGameCreationResult(lobby, hostPlayerId, lobbyCode) {
  try {
    // Save game creation to API (async, don't wait for it)
    gameDataService.saveGameCreation(lobby, hostPlayerId).catch(error => {
      console.error(`[GAME_DATA] Failed to save game creation for ${lobbyCode}:`, error.message);
    });

    console.log(`[GAME_DATA] Game creation save initiated for ${lobbyCode}`);
  } catch (error) {
    console.error(`[GAME_DATA] Error initiating game creation save for ${lobbyCode}:`, error.message);
  }
}

module.exports = {
  saveGameEndResult,
  saveHandEndResult,
  saveCardPlayResult,
  saveJordhiCallResult,
  saveGameStartResult,
  savePlayerJoinResult,
  saveGameCreationResult
};
