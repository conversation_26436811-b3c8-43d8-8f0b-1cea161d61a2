/**
 * <PERSON><PERSON> for processing 4-ball claims
 */

/**
 * Process a 4-ball claim
 * @param {Object} io - Socket.io instance
 * @param {Object} lobby - The game lobby
 * @param {Object} matchedLobby - The matched lobby
 * @param {String} lobbyCode - The lobby code
 * @param {Object} player - The player making the claim
 * @param {String} socketId - The socket ID of the player making the claim
 * @param {Object} data - The claim data
 * @param {Object} validationResult - The result of the validation
 * @param {String} eventName - The event name to emit
 * @param {Function} resetUtils - The reset utilities
 * @param {Function} callback - The callback function
 */
function processFourBallClaim(
  io,
  lobby,
  matchedLobby,
  lobbyCode,
  player,
  socketId,
  data,
  validationResult,
  eventName,
  resetUtils,
  callback
) {
  try {
    // Set flag to indicate 4-ball processing is in progress
    lobby.fourBallInProgress = true;
    if (matchedLobby && matchedLobby !== lobby) {
      matchedLobby.fourBallInProgress = true;
    }

    // IMMEDIATELY stop any active timers to prevent timeout events
    const turnUtils = require('../utils/turnUtils');
    turnUtils.stopTurnTimer(lobby);
    if (matchedLobby && matchedLobby !== lobby) {
      turnUtils.stopTurnTimer(matchedLobby);
    }
    console.log('Stopped all timers immediately upon 4-ball claim');

    // Find the accused player
    const accusedPlayer = lobby.players.find(p => p.id === data.accusedPlayerId);
    if (!accusedPlayer) {
      console.error(`Accused player ${data.accusedPlayerId} not found`);
      // Clear the flag before returning
      lobby.fourBallInProgress = false;
      if (matchedLobby && matchedLobby !== lobby) {
        matchedLobby.fourBallInProgress = false;
      }
      return callback?.({ success: false, error: 'Accused player not found' });
    }

    const accusedTeam = accusedPlayer.team;
    const accuserTeam = player.team;

    // Initialize ball scores if not already done
    if (!lobby.ballScores) {
      lobby.ballScores = { team1: 0, team2: 0 };
      matchedLobby.ballScores = { team1: 0, team2: 0 };
    }

    // Determine the winning team based on validation result
    // If the accusation is valid, the accuser's team wins 4 balls
    // If the accusation is invalid, the accused's team wins 4 balls
    const winningTeam = validationResult.isValid ? accuserTeam : accusedTeam;

    // Check if we're dealing with a single lobby case (all 4 players in one lobby)
    const isSingleLobby = (lobby === matchedLobby);
    console.log(`Is single lobby case: ${isSingleLobby}`);

    // Award 4 balls to the winning team
    lobby.ballScores[`team${winningTeam}`] = 4; // Set to exactly 4 balls, not increment

    // Only update matchedLobby if it's different from lobby
    if (!isSingleLobby) {
        matchedLobby.ballScores[`team${winningTeam}`] = 4;
    }

    // Reset the other team's score to 0
    const losingTeam = winningTeam === 1 ? 2 : 1;
    lobby.ballScores[`team${losingTeam}`] = 0;

    // Only update matchedLobby if it's different from lobby
    if (!isSingleLobby) {
        matchedLobby.ballScores[`team${losingTeam}`] = 0;
    }

    console.log(`Awarded 4 balls to Team ${winningTeam} for "${data.option}" claim. Validation result:`, validationResult);

    // Prepare the result data
    const resultData = {
      ballType: data.ballType,
      option: data.option,
      targetPlayer: data.accusedPlayerId,
      targetPlayerName: accusedPlayer.name,
      targetTeam: accusedTeam,
      accuserId: socketId,
      accuserName: player.name,
      accuserTeam: accuserTeam,
      handNumber: data.handNumber,
      isValid: validationResult.isValid,
      winningTeam,
      ballsAwarded: 4,
      ballScores: lobby.ballScores,
      ...validationResult // Include all validation result properties
    };

    // Notify all players in both lobbies
    io.to(lobbyCode).emit(eventName, resultData);

    if (matchedLobby && matchedLobby.lobbyCode !== lobbyCode) {
      io.to(matchedLobby.lobbyCode).emit(eventName, resultData);
    }

    // Reset for next ball
    resetUtils.resetBallState(lobby, matchedLobby);

    // Reset hand counter and points
    lobby.currentHandId = 0;
    matchedLobby.currentHandId = 0;
    lobby.ballPoints = { team1: 0, team2: 0 };
    matchedLobby.ballPoints = { team1: 0, team2: 0 };

    // Get the current ball ID
    const ballId = (lobby.currentBallId || 0) + 1;
    lobby.currentBallId = ballId;
    matchedLobby.currentBallId = ballId;

    // Send ball_completed event to both lobbies
    const ballCompletedData = {
      ballId,
      winner: winningTeam,
      points: {
        team1: 0,
        team2: 0
      },
      nextDealer: lobby.dealerId, // Keep the same dealer
      ballScores: lobby.ballScores,
      fourBallAwarded: true,
      fourBallOption: data.option,
      fourBallWinningTeam: winningTeam,
      ballsAwarded: 4
    };

    io.to(lobbyCode).emit('ball_completed', ballCompletedData);
    io.to(matchedLobby.lobbyCode).emit('ball_completed', ballCompletedData);

    // Add ball to game history and check for game end
    const gameEndUtils = require('../utils/gameEndUtils');
    gameEndUtils.addBallToHistory(lobby, ballCompletedData);
    if (matchedLobby !== lobby) {
      gameEndUtils.addBallToHistory(matchedLobby, ballCompletedData);
    }

    // Check if game should end
    const gameEndCheck = gameEndUtils.checkGameEnd(lobby);
    if (gameEndCheck.gameEnded) {
      console.log(`Game ended! Team ${gameEndCheck.winner} wins!`);

      const gameHistory = gameEndUtils.getGameHistory(lobby);
      const gameEndData = {
        ...gameEndCheck,
        gameHistory
      };

      // Emit game_ended event to both lobbies
      io.to(lobbyCode).emit('game_ended', gameEndData);
      io.to(matchedLobby.lobbyCode).emit('game_ended', gameEndData);

      // Clear the 4-ball processing flag
      lobby.fourBallInProgress = false;
      if (matchedLobby && matchedLobby !== lobby) {
        matchedLobby.fourBallInProgress = false;
      }

      return { success: true, gameEnded: true };
    }

    // Clear the 4-ball processing flag
    lobby.fourBallInProgress = false;
    if (matchedLobby && matchedLobby !== lobby) {
      matchedLobby.fourBallInProgress = false;
    }

    // Reset game phase to shuffle for the next ball after a delay
    setTimeout(() => {
      io.to(lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
      io.to(matchedLobby.lobbyCode).emit('game_phase_updated', { phase: 'shuffle' });
    }, 15000); // 15 second delay before resetting

    return { success: true };
  } catch (error) {
    console.error('Error processing 4-ball claim:', error);

    // Clear the 4-ball processing flag in case of error
    lobby.fourBallInProgress = false;
    if (matchedLobby && matchedLobby !== lobby) {
      matchedLobby.fourBallInProgress = false;
    }

    return { success: false, error: error.message };
  }
}

module.exports = {
  processFourBallClaim
};
