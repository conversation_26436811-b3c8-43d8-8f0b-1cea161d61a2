/**
 * Handler for Double call functionality
 */

const gameEndUtils = require('../utils/gameEndUtils');

/**
 * Validate a Double call and determine the outcome
 * @param {Object} lobby - The game lobby
 * @param {Object} matchedLobby - The matched game lobby
 * @param {String} playerId - The ID of the player who called Double
 * @returns {Object} The result of the Double call validation
 */
function validateDoubleCall(lobby, matchedLobby, playerId) {
  // Find the player who called Double
  const player = lobby.players.find(p => p.id === playerId);
  if (!player) {
    console.error(`Player ${playerId} not found in lobby`);
    return {
      isValid: false,
      reason: 'Player not found',
      playerTeam: null
    };
  }

  const playerTeam = player.team;
  const opposingTeam = playerTeam === 1 ? 2 : 1;

  // Check if this is the last hand (6th hand)
  if (!lobby.currentHandId || lobby.currentHandId < 5) {
    console.error(`Double can only be called in the last hand. Current hand: ${lobby.currentHandId}`);
    return {
      isValid: false,
      reason: 'Double can only be called in the last hand',
      playerTeam,
      opposingTeam
    };
  }

  // Make sure we have hands data
  if (!lobby.hands || lobby.hands.length === 0) {
    console.error('No hands data available');
    return {
      isValid: false,
      reason: 'No hands data available',
      playerTeam,
      opposingTeam
    };
  }

  // Get unique hands won by each team to avoid counting duplicates
  const team1HandIds = new Set();
  const team2HandIds = new Set();
  const team1Hands = [];
  const team2Hands = [];

  lobby.hands.forEach(hand => {
    if (hand.winningTeam === 1) {
      team1HandIds.add(hand.id);
      team1Hands.push(hand);
    } else if (hand.winningTeam === 2) {
      team2HandIds.add(hand.id);
      team2Hands.push(hand);
    }
  });

  const team1HandsWon = team1HandIds.size;
  const team2HandsWon = team2HandIds.size;

  // Get the hands won by the opposing team
  const opposingTeamHandsWon = opposingTeam === 1 ? team1HandsWon : team2HandsWon;
  const opposingTeamHands = opposingTeam === 1 ? team1Hands : team2Hands;

  // Check if the opposing team has won any hands
  if (opposingTeamHandsWon > 0) {
    console.log(`Opposing team (${opposingTeam}) has won ${opposingTeamHandsWon} hands. Double call will result in opposing team winning 4 balls.`);

    // Double is still valid, but will result in the opposing team winning 4 balls
    return {
      isValid: true,
      reason: 'Double call is valid, but opposing team has already won hands',
      playerTeam,
      opposingTeam,
      opposingTeamHasWonHands: true,
      opposingTeamHands: opposingTeamHands
    };
  }

  // The call is valid at this point - the actual outcome will be determined after the hand is played
  return {
    isValid: true,
    reason: 'Double call is valid',
    playerTeam,
    opposingTeam,
    opposingTeamHasWonHands: false,
    opposingTeamHands: []
  };
}

/**
 * Process the outcome of a Double call after the hand is completed
 * @param {Object} lobby - The game lobby
 * @param {Object} matchedLobby - The matched game lobby
 * @param {String} doubleCallerId - The ID of the player who called Double
 * @param {String} handWinnerId - The ID of the player who won the hand
 * @returns {Object} The result of the Double call
 */
function processDoubleOutcome(lobby, matchedLobby, doubleCallerId, handWinnerId) {
  // Find the players
  const doubleCallerPlayer = lobby.players.find(p => p.id === doubleCallerId);
  const handWinnerPlayer = lobby.players.find(p => p.id === handWinnerId);
  const currentDealer = lobby.players.find(p => p.id === lobby.dealerId);

  if (!doubleCallerPlayer || !handWinnerPlayer) {
    console.error(`Player not found. Double caller: ${doubleCallerId}, Hand winner: ${handWinnerId}`);
    return { success: false, error: 'Player not found' };
  }

  // Log the current dealer information
  if (currentDealer) {
    console.log(`Current dealer in processDoubleOutcome: ${currentDealer.name} (${currentDealer.id}) - Team ${currentDealer.team}`);
  } else {
    console.log(`No current dealer found in processDoubleOutcome. Dealer ID: ${lobby.dealerId}`);
  }

  const doubleCallerTeam = doubleCallerPlayer.team;
  const handWinnerTeam = handWinnerPlayer.team;
  const opposingTeam = doubleCallerTeam === 1 ? 2 : 1;

  // Initialize ball scores if not already done
  if (!lobby.ballScores) {
    lobby.ballScores = { team1: 0, team2: 0 };
    matchedLobby.ballScores = { team1: 0, team2: 0 };
  }

  let ballsAwarded = 0;
  let winningTeam = null;
  let outcome = '';

  // Get hands won by each team
  const team1HandIds = new Set();
  const team2HandIds = new Set();
  const team1Hands = [];
  const team2Hands = [];

  lobby.hands.forEach(hand => {
    if (hand.winningTeam === 1) {
      team1HandIds.add(hand.id);
      team1Hands.push(hand);
    } else if (hand.winningTeam === 2) {
      team2HandIds.add(hand.id);
      team2Hands.push(hand);
    }
  });

  const opposingTeamHandsWon = opposingTeam === 1 ? team1HandIds.size : team2HandIds.size;
  const opposingTeamHands = opposingTeam === 1 ? team1Hands : team2Hands;

  // Check if the opposing team has won any hands
  if (opposingTeamHandsWon > 0) {
    // If opposing team has won any hands, they automatically win 4 balls
    ballsAwarded = 4;
    winningTeam = opposingTeam;
    outcome = 'opposing_team_won_previous_hands';

    // Set ball scores directly
    lobby.ballScores[`team${opposingTeam}`] = ballsAwarded;
    matchedLobby.ballScores[`team${opposingTeam}`] = ballsAwarded;

    // Reset the double caller's team score to 0
    lobby.ballScores[`team${doubleCallerTeam}`] = 0;
    matchedLobby.ballScores[`team${doubleCallerTeam}`] = 0;

    console.log(`Double automatic failure! ${doubleCallerPlayer.name} (Team ${doubleCallerTeam}) called Double but opposing team had already won ${opposingTeamHandsWon} hands. Team ${opposingTeam} awarded ${ballsAwarded} balls total.`);

    // Return early with the opposing team hands
    return {
      success: true,
      doubleCallerId,
      doubleCallerName: doubleCallerPlayer.name,
      doubleCallerTeam,
      handWinnerId,
      handWinnerName: handWinnerPlayer.name,
      handWinnerTeam,
      ballsAwarded,
      winningTeam,
      outcome,
      opposingTeamHasWonHands: true,
      opposingTeamHands,
      lastHand: lobby.currentHandCards ? {
        id: lobby.currentHandId,
        winner: handWinnerPlayer,
        cards: lobby.currentHandCards,
        winningTeam: handWinnerTeam
      } : null,
      ballScores: lobby.ballScores,
      currentDealerId: lobby.dealerId,
      currentDealerTeam: currentDealer ? currentDealer.team : null
    };
  }

  // Check for special ball limit rules before proceeding with normal Double rules
  const specialRulesCheck = gameEndUtils.checkSpecialBallLimitRules(lobby, doubleCallerTeam, 'double');

  if (specialRulesCheck.applySpecialRules) {
    // Special rules apply - opposing team gets 4 balls, caller gets none
    ballsAwarded = specialRulesCheck.ballsAwarded;
    winningTeam = specialRulesCheck.winningTeam;
    outcome = specialRulesCheck.outcome;

    // Set ball scores directly
    lobby.ballScores[`team${specialRulesCheck.winningTeam}`] = ballsAwarded;
    matchedLobby.ballScores[`team${specialRulesCheck.winningTeam}`] = ballsAwarded;

    // Reset the double caller's team score to 0
    lobby.ballScores[`team${doubleCallerTeam}`] = 0;
    matchedLobby.ballScores[`team${doubleCallerTeam}`] = 0;

    console.log(`Double special rule applied! ${doubleCallerPlayer.name} (Team ${doubleCallerTeam}) called Double but special ball limit rules apply. ${specialRulesCheck.reason}. Team ${specialRulesCheck.winningTeam} awarded ${ballsAwarded} balls total.`);

    return {
      success: true,
      doubleCallerId,
      doubleCallerName: doubleCallerPlayer.name,
      doubleCallerTeam,
      handWinnerId,
      handWinnerName: handWinnerPlayer.name,
      handWinnerTeam,
      ballsAwarded,
      winningTeam,
      outcome,
      opposingTeamHasWonHands: false,
      opposingTeamHands: [],
      specialRuleApplied: true,
      specialRuleReason: specialRulesCheck.reason,
      lastHand: lobby.currentHandCards ? {
        id: lobby.currentHandId,
        winner: handWinnerPlayer,
        cards: lobby.currentHandCards,
        winningTeam: handWinnerTeam
      } : null,
      ballScores: lobby.ballScores,
      currentDealerId: lobby.dealerId,
      currentDealerTeam: currentDealer ? currentDealer.team : null
    };
  }

  // If opposing team hasn't won any hands and no special rules apply, proceed with normal Double rules
  // Case 1: Double caller won the hand
  if (doubleCallerId === handWinnerId) {
    // Double caller's team wins 2 balls TOTAL
    ballsAwarded = 2;
    winningTeam = doubleCallerTeam;
    outcome = 'success';

    // Set ball scores directly
    lobby.ballScores[`team${doubleCallerTeam}`] = ballsAwarded;
    matchedLobby.ballScores[`team${doubleCallerTeam}`] = ballsAwarded;

    // Reset the opposing team's score to 0
    lobby.ballScores[`team${opposingTeam}`] = 0;
    matchedLobby.ballScores[`team${opposingTeam}`] = 0;

    console.log(`Double success! ${doubleCallerPlayer.name} (Team ${doubleCallerTeam}) called Double and won the hand. Team ${doubleCallerTeam} awarded ${ballsAwarded} balls total.`);
  }
  // Case 2: Double caller's teammate won the hand
  else if (handWinnerTeam === doubleCallerTeam) {
    // Double caller's team loses, opposing team wins 4 balls TOTAL
    ballsAwarded = 4;
    winningTeam = opposingTeam;
    outcome = 'teammate_won';

    // Set ball scores directly
    lobby.ballScores[`team${opposingTeam}`] = ballsAwarded;
    matchedLobby.ballScores[`team${opposingTeam}`] = ballsAwarded;

    // Reset the double caller's team score to 0
    lobby.ballScores[`team${doubleCallerTeam}`] = 0;
    matchedLobby.ballScores[`team${doubleCallerTeam}`] = 0;

    console.log(`Double failure! ${doubleCallerPlayer.name} (Team ${doubleCallerTeam}) called Double but their teammate ${handWinnerPlayer.name} won the hand. Team ${opposingTeam} awarded ${ballsAwarded} balls total.`);
  }
  // Case 3: Opponent won the hand
  else {
    // Double caller's team loses, opposing team wins 4 balls TOTAL
    ballsAwarded = 4;
    winningTeam = opposingTeam;
    outcome = 'opponent_won';

    // Set ball scores directly
    lobby.ballScores[`team${opposingTeam}`] = ballsAwarded;
    matchedLobby.ballScores[`team${opposingTeam}`] = ballsAwarded;

    // Reset the double caller's team score to 0
    lobby.ballScores[`team${doubleCallerTeam}`] = 0;
    matchedLobby.ballScores[`team${doubleCallerTeam}`] = 0;

    console.log(`Double failure! ${doubleCallerPlayer.name} (Team ${doubleCallerTeam}) called Double but opponent ${handWinnerPlayer.name} (Team ${handWinnerTeam}) won the hand. Team ${opposingTeam} awarded ${ballsAwarded} balls total.`);
  }

  return {
    success: true,
    doubleCallerId,
    doubleCallerName: doubleCallerPlayer.name,
    doubleCallerTeam,
    handWinnerId,
    handWinnerName: handWinnerPlayer.name,
    handWinnerTeam,
    ballsAwarded,
    winningTeam,
    outcome,
    opposingTeamHasWonHands: false,
    opposingTeamHands: [],
    lastHand: lobby.currentHandCards ? {
      id: lobby.currentHandId,
      winner: handWinnerPlayer,
      cards: lobby.currentHandCards,
      winningTeam: handWinnerTeam
    } : null,
    ballScores: lobby.ballScores,
    currentDealerId: lobby.dealerId,
    currentDealerTeam: currentDealer ? currentDealer.team : null
  };
}

module.exports = {
  validateDoubleCall,
  processDoubleOutcome
};
