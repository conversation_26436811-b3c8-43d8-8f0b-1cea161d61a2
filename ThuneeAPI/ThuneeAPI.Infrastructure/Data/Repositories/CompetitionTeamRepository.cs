using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Data.Repositories;

/// <summary>
/// Repository implementation for CompetitionTeam entity operations using Dapper
/// </summary>
public class CompetitionTeamRepository : BaseRepository, ICompetitionTeamRepository
{
    public CompetitionTeamRepository(IDbConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    public async Task<CompetitionTeam> CreateAsync(CompetitionTeam competitionTeam)
    {
        var parameters = new
        {
            Id = competitionTeam.Id,
            CompetitionId = competitionTeam.CompetitionId,
            TeamName = competitionTeam.TeamName,
            Player1Id = competitionTeam.Player1Id,
            InviteCode = competitionTeam.InviteCode,
            MaxGames = competitionTeam.MaxGames
        };

        var result = await ExecuteStoredProcedureFirstOrDefaultAsync<CompetitionTeam>("SP_CreateCompetitionTeam", parameters);
        return result ?? throw new InvalidOperationException("Failed to create competition team");
    }

    public async Task<CompetitionTeam?> GetByIdAsync(Guid id)
    {
        var sql = "SELECT * FROM CompetitionTeams WHERE Id = @Id";
        var parameters = new { Id = id };
        var teams = await ExecuteQueryAsync<CompetitionTeam>(sql, parameters);
        return teams.FirstOrDefault();
    }

    public async Task<CompetitionTeam?> GetByInviteCodeAsync(string inviteCode)
    {
        var sql = "SELECT * FROM CompetitionTeams WHERE InviteCode = @InviteCode";
        var parameters = new { InviteCode = inviteCode };
        var teams = await ExecuteQueryAsync<CompetitionTeam>(sql, parameters);
        return teams.FirstOrDefault();
    }

    public async Task<IEnumerable<CompetitionTeam>> GetByCompetitionIdAsync(Guid competitionId)
    {
        var sql = "SELECT * FROM CompetitionTeams WHERE CompetitionId = @CompetitionId ORDER BY RegisteredAt DESC";
        var parameters = new { CompetitionId = competitionId };
        return await ExecuteQueryAsync<CompetitionTeam>(sql, parameters);
    }

    public async Task<CompetitionTeam?> GetByPlayerAndCompetitionAsync(Guid competitionId, Guid playerId)
    {
        var sql = @"SELECT * FROM CompetitionTeams 
                   WHERE CompetitionId = @CompetitionId 
                   AND (Player1Id = @PlayerId OR Player2Id = @PlayerId)";
        var parameters = new { CompetitionId = competitionId, PlayerId = playerId };
        var teams = await ExecuteQueryAsync<CompetitionTeam>(sql, parameters);
        return teams.FirstOrDefault();
    }

    public async Task<CompetitionTeam> UpdateAsync(CompetitionTeam competitionTeam)
    {
        var sql = @"
            UPDATE CompetitionTeams 
            SET TeamName = @TeamName,
                Player2Id = @Player2Id,
                GamesPlayed = @GamesPlayed,
                Points = @Points,
                BonusPoints = @BonusPoints,
                IsComplete = @IsComplete,
                CompletedAt = @CompletedAt
            WHERE Id = @Id";

        var parameters = new
        {
            Id = competitionTeam.Id,
            TeamName = competitionTeam.TeamName,
            Player2Id = competitionTeam.Player2Id,
            GamesPlayed = competitionTeam.GamesPlayed,
            Points = competitionTeam.Points,
            BonusPoints = competitionTeam.BonusPoints,
            IsComplete = competitionTeam.IsComplete,
            CompletedAt = competitionTeam.CompletedAt
        };

        await ExecuteQueryAsync<object>(sql, parameters);
        return competitionTeam;
    }

    public async Task<CompetitionTeam> JoinTeamAsync(string inviteCode, Guid player2Id)
    {
        var parameters = new
        {
            InviteCode = inviteCode,
            Player2Id = player2Id
        };

        var result = await ExecuteStoredProcedureFirstOrDefaultAsync<CompetitionTeam>("SP_JoinCompetitionTeam", parameters);
        return result ?? throw new InvalidOperationException("Failed to join competition team");
    }
}
