using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Data.Repositories;

/// <summary>
/// Repository implementation for Competition entity operations using Dapper
/// </summary>
public class CompetitionRepository : BaseRepository, ICompetitionRepository
{
    public CompetitionRepository(IDbConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    public async Task<Competition> CreateAsync(Competition competition)
    {
        var parameters = new
        {
            Id = competition.Id,
            Name = competition.Name,
            Description = competition.Description,
            StartDate = competition.StartDate,
            EndDate = competition.EndDate,
            MaxTeams = competition.MaxTeams,
            EntryFee = competition.EntryFee,
            PrizeFirst = competition.PrizeFirst,
            PrizeSecond = competition.PrizeSecond,
            PrizeThird = competition.PrizeThird,
            TotalPrizePool = competition.TotalPrizePool,
            IsPublic = competition.IsPublic,
            AllowSpectators = competition.AllowSpectators
        };

        var result = await ExecuteStoredProcedureFirstOrDefaultAsync<Competition>("SP_CreateCompetition", parameters);
        return result ?? throw new InvalidOperationException("Failed to create competition");
    }

    public async Task<Competition?> GetByIdAsync(Guid id)
    {
        var sql = "SELECT * FROM Competitions WHERE Id = @Id";
        var parameters = new { Id = id };
        var competitions = await ExecuteQueryAsync<Competition>(sql, parameters);
        return competitions.FirstOrDefault();
    }

    public async Task<IEnumerable<Competition>> GetAllAsync()
    {
        // Temporary fix: Use direct SQL query instead of stored procedure
        // TODO: Switch back to stored procedure once SP_GetCompetitions is created in database
        var sql = "SELECT * FROM Competitions ORDER BY CreatedAt DESC";
        return await ExecuteQueryAsync<Competition>(sql);

        // Original stored procedure call (commented out until SP is created):
        // return await ExecuteStoredProcedureAsync<Competition>("SP_GetCompetitions");
    }

    public async Task<IEnumerable<Competition>> GetByStatusAsync(string status)
    {
        var sql = "SELECT * FROM Competitions WHERE Status = @Status ORDER BY CreatedAt DESC";
        var parameters = new { Status = status };
        return await ExecuteQueryAsync<Competition>(sql, parameters);
    }

    public async Task UpdateAsync(Competition competition)
    {
        var sql = @"
            UPDATE Competitions 
            SET Name = @Name,
                Description = @Description,
                StartDate = @StartDate,
                EndDate = @EndDate,
                MaxTeams = @MaxTeams,
                EntryFee = @EntryFee,
                PrizeFirst = @PrizeFirst,
                PrizeSecond = @PrizeSecond,
                PrizeThird = @PrizeThird,
                TotalPrizePool = @TotalPrizePool,
                IsPublic = @IsPublic,
                AllowSpectators = @AllowSpectators,
                Status = @Status,
                CurrentTeams = @CurrentTeams,
                UpdatedAt = GETUTCDATE()
            WHERE Id = @Id";

        var parameters = new
        {
            Id = competition.Id,
            Name = competition.Name,
            Description = competition.Description,
            StartDate = competition.StartDate,
            EndDate = competition.EndDate,
            MaxTeams = competition.MaxTeams,
            EntryFee = competition.EntryFee,
            PrizeFirst = competition.PrizeFirst,
            PrizeSecond = competition.PrizeSecond,
            PrizeThird = competition.PrizeThird,
            TotalPrizePool = competition.TotalPrizePool,
            IsPublic = competition.IsPublic,
            AllowSpectators = competition.AllowSpectators,
            Status = competition.Status,
            CurrentTeams = competition.CurrentTeams
        };

        await ExecuteCommandAsync(sql, parameters);
    }

    public async Task DeleteAsync(Guid id)
    {
        var sql = "DELETE FROM Competitions WHERE Id = @Id";
        var parameters = new { Id = id };
        await ExecuteCommandAsync(sql, parameters);
    }

    public async Task<IEnumerable<Competition>> GetPublicCompetitionsAsync()
    {
        var sql = "SELECT * FROM Competitions WHERE IsPublic = 1 ORDER BY StartDate ASC";
        return await ExecuteQueryAsync<Competition>(sql);
    }

    public async Task<IEnumerable<Competition>> GetUpcomingCompetitionsAsync()
    {
        var sql = "SELECT * FROM Competitions WHERE Status = 'upcoming' ORDER BY StartDate ASC";
        return await ExecuteQueryAsync<Competition>(sql);
    }

    public async Task<IEnumerable<Competition>> GetActiveCompetitionsAsync()
    {
        var sql = "SELECT * FROM Competitions WHERE Status = 'active' ORDER BY StartDate ASC";
        return await ExecuteQueryAsync<Competition>(sql);
    }
}
