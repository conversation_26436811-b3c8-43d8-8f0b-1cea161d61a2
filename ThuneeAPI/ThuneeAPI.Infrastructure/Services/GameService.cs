using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Services;

public class GameService : IGameService
{
    private readonly IGameRepository _gameRepository;
    private readonly IUserRepository _userRepository;

    public GameService(IGameRepository gameRepository, IUserRepository userRepository)
    {
        _gameRepository = gameRepository;
        _userRepository = userRepository;
    }

    public async Task<GameDto> CreateGameAsync(CreateGameDto createGameDto, Guid hostPlayerId)
    {
        // Use provided lobby code or generate a new one
        var lobbyCode = !string.IsNullOrEmpty(createGameDto.LobbyCode)
            ? createGameDto.LobbyCode
            : await GenerateLobbyCodeAsync();

        var game = new Game
        {
            Id = Guid.NewGuid(),
            LobbyCode = lobbyCode,
            Team1Name = createGameDto.Team1Name,
            Team2Name = "Team 2",
            CompetitionId = createGameDto.CompetitionId,
            Team1Player1Id = hostPlayerId,
            Team1Player2Id = null,
            Team2Player1Id = null,
            Team2Player2Id = null,
            Status = "waiting",
            CurrentBall = 1,
            CurrentHand = 1,
            Team1Score = 0,
            Team2Score = 0,
            Team1BallsWon = 0,
            Team2BallsWon = 0,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        var createdGame = await _gameRepository.CreateAsync(game);
        return await MapToGameDto(createdGame);
    }

    public async Task<GameDto> GetGameByLobbyCodeAsync(string lobbyCode)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(lobbyCode);

        if (game == null)
            throw new ArgumentException("Game not found");

        return await MapToGameDto(game);
    }

    public async Task<GameDto> JoinGameAsync(JoinGameDto joinGameDto, Guid playerId)
    {
        var game = await _gameRepository.JoinGameAsync(joinGameDto.LobbyCode, playerId);

        if (game == null)
            throw new ArgumentException("Game not found or could not join");

        return await MapToGameDto(game);
    }

    public async Task<GameDto> StartGameAsync(string lobbyCode, Guid hostPlayerId)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(lobbyCode);

        if (game == null)
            throw new ArgumentException("Game not found");

        if (game.Team1Player1Id != hostPlayerId)
            throw new UnauthorizedAccessException("Only the host can start the game");

        if (!game.Team1Player2Id.HasValue || !game.Team2Player1Id.HasValue || !game.Team2Player2Id.HasValue)
            throw new InvalidOperationException("Game needs 4 players to start");

        game.Status = "in_progress";
        game.StartedAt = DateTime.UtcNow;
        game.UpdatedAt = DateTime.UtcNow;

        await _gameRepository.UpdateAsync(game);

        return await MapToGameDto(game);
    }

    public async Task<GameDto> RecordHandResultAsync(RecordHandResultDto handResultDto)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(handResultDto.LobbyCode);

        if (game == null)
            throw new ArgumentException("Game not found");

        // Record hand result using stored procedure
        await _gameRepository.RecordHandResultAsync(
            game.Id,
            handResultDto.BallNumber,
            handResultDto.HandNumber,
            handResultDto.WinnerPlayerId,
            handResultDto.Points);

        // Get updated game
        var updatedGame = await _gameRepository.GetByIdAsync(game.Id);
        return await MapToGameDto(updatedGame!);
    }

    public async Task<GameDto> RecordGameResultAsync(GameResultDto gameResultDto)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(gameResultDto.LobbyCode);

        if (game == null)
            throw new ArgumentException("Game not found");

        game.Status = "completed";
        game.WinnerTeam = gameResultDto.WinnerTeam;
        game.Team1Score = gameResultDto.Team1FinalScore;
        game.Team2Score = gameResultDto.Team2FinalScore;
        game.CompletedAt = DateTime.UtcNow;
        game.UpdatedAt = DateTime.UtcNow;

        await _gameRepository.UpdateAsync(game);

        return await MapToGameDto(game);
    }

    public async Task<List<GameDto>> GetUserGamesAsync(Guid userId, int page = 1, int pageSize = 20)
    {
        var games = await _gameRepository.GetByPlayerIdAsync(userId);

        var gameDtos = new List<GameDto>();
        foreach (var game in games.Skip((page - 1) * pageSize).Take(pageSize))
        {
            gameDtos.Add(await MapToGameDto(game));
        }

        return gameDtos;
    }

    public async Task<List<GameHandDto>> GetGameHandsAsync(string lobbyCode)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(lobbyCode);

        if (game == null)
            throw new ArgumentException("Game not found");

        // For now, return empty list - this would need additional repository methods
        // to implement game hands and played cards functionality
        return new List<GameHandDto>();
    }

    public async Task<bool> IsPlayerInGameAsync(Guid playerId, string lobbyCode)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(lobbyCode);

        if (game == null)
            return false;

        return game.Team1Player1Id == playerId ||
               game.Team1Player2Id == playerId ||
               game.Team2Player1Id == playerId ||
               game.Team2Player2Id == playerId;
    }

    public async Task<string> GenerateLobbyCodeAsync()
    {
        string code;
        bool exists;
        do
        {
            code = GenerateRandomCode();
            exists = await _gameRepository.LobbyCodeExistsAsync(code);
        } while (exists);

        return code;
    }

    public async Task<List<GameDto>> GetActiveGamesAsync()
    {
        var games = await _gameRepository.GetActiveGamesAsync();

        var gameDtos = new List<GameDto>();
        foreach (var game in games)
        {
            gameDtos.Add(await MapToGameDto(game));
        }

        return gameDtos;
    }

    public async Task LeaveGameAsync(string lobbyCode, Guid playerId)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(lobbyCode);

        if (game == null)
            throw new ArgumentException("Game not found");

        // Remove player from their slot
        if (game.Team1Player1Id == playerId)
            game.Team1Player1Id = null;
        else if (game.Team1Player2Id == playerId)
            game.Team1Player2Id = null;
        else if (game.Team2Player1Id == playerId)
            game.Team2Player1Id = null;
        else if (game.Team2Player2Id == playerId)
            game.Team2Player2Id = null;
        else
            throw new ArgumentException("Player not found in this game");

        game.UpdatedAt = DateTime.UtcNow;
        await _gameRepository.UpdateAsync(game);
    }

    public async Task SetPlayerReadyAsync(string lobbyCode, Guid playerId, bool ready)
    {
        var game = await _gameRepository.GetByLobbyCodeAsync(lobbyCode);

        if (game == null)
            throw new ArgumentException("Game not found");

        // For now, just update the game timestamp
        // In a full implementation, you'd track ready status per player
        game.UpdatedAt = DateTime.UtcNow;
        await _gameRepository.UpdateAsync(game);
    }

    private static string GenerateRandomCode()
    {
        var random = new Random();
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        return new string(Enumerable.Repeat(chars, 6)
            .Select(s => s[random.Next(s.Length)]).ToArray());
    }

    private static bool IsPlayerInTeam1(Game game, Guid playerId)
    {
        return game.Team1Player1Id == playerId || game.Team1Player2Id == playerId;
    }

    private async Task<GameDto> MapToGameDto(Game game)
    {
        var gameDto = new GameDto
        {
            Id = game.Id,
            LobbyCode = game.LobbyCode,
            CompetitionId = game.CompetitionId,
            Team1Name = game.Team1Name,
            Team2Name = game.Team2Name,
            Status = game.Status,
            TrumpSuit = game.TrumpSuit,
            CurrentBall = game.CurrentBall,
            CurrentHand = game.CurrentHand,
            Team1Score = game.Team1Score,
            Team2Score = game.Team2Score,
            Team1BallsWon = game.Team1BallsWon,
            Team2BallsWon = game.Team2BallsWon,
            WinnerTeam = game.WinnerTeam,
            StartedAt = game.StartedAt,
            CompletedAt = game.CompletedAt,
            CreatedAt = game.CreatedAt
        };

        // Load player information
        if (game.Team1Player1Id.HasValue)
        {
            var player = await _userRepository.GetByIdAsync(game.Team1Player1Id.Value);
            if (player != null)
            {
                gameDto.Team1Players.Add(new PlayerDto { Id = player.Id, Username = player.Username });
            }
        }

        if (game.Team1Player2Id.HasValue)
        {
            var player = await _userRepository.GetByIdAsync(game.Team1Player2Id.Value);
            if (player != null)
            {
                gameDto.Team1Players.Add(new PlayerDto { Id = player.Id, Username = player.Username });
            }
        }

        if (game.Team2Player1Id.HasValue)
        {
            var player = await _userRepository.GetByIdAsync(game.Team2Player1Id.Value);
            if (player != null)
            {
                gameDto.Team2Players.Add(new PlayerDto { Id = player.Id, Username = player.Username });
            }
        }

        if (game.Team2Player2Id.HasValue)
        {
            var player = await _userRepository.GetByIdAsync(game.Team2Player2Id.Value);
            if (player != null)
            {
                gameDto.Team2Players.Add(new PlayerDto { Id = player.Id, Username = player.Username });
            }
        }

        return gameDto;
    }
}
