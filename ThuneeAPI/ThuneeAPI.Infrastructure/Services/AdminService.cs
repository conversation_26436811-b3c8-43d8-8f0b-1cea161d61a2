using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Services;

public class AdminService : IAdminService
{
    private readonly IUserRepository _userRepository;
    private readonly ICompetitionRepository _competitionRepository;
    private readonly IEmailService _emailService;
    private readonly ILogger<AdminService> _logger;

    public AdminService(
        IUserRepository userRepository,
        ICompetitionRepository competitionRepository,
        IEmailService emailService,
        ILogger<AdminService> logger)
    {
        _userRepository = userRepository;
        _competitionRepository = competitionRepository;
        _emailService = emailService;
        _logger = logger;
    }

    // User Management
    public async Task<List<AdminUserDto>> GetAllUsersAsync()
    {
        try
        {
            // This would need to be implemented in the user repository
            // For now, return mock data
            var mockUsers = new List<AdminUserDto>
            {
                new AdminUserDto
                {
                    Id = Guid.NewGuid(),
                    Username = "admin",
                    Email = "<EMAIL>",
                    IsVerified = true,
                    IsActive = true,
                    IsAdmin = true,
                    LastLoginAt = DateTime.UtcNow.AddHours(-1),
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddHours(-1)
                },
                new AdminUserDto
                {
                    Id = Guid.NewGuid(),
                    Username = "Sherisan",
                    Email = "<EMAIL>",
                    IsVerified = true,
                    IsActive = true,
                    IsAdmin = false,
                    LastLoginAt = DateTime.UtcNow.AddHours(-2),
                    CreatedAt = DateTime.UtcNow.AddDays(-25),
                    UpdatedAt = DateTime.UtcNow.AddHours(-2)
                }
            };

            return mockUsers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all users");
            throw;
        }
    }

    public async Task<AdminUserDto?> GetUserByIdAsync(Guid userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null) return null;

            return new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<AdminUserDto> UpdateUserAsync(Guid userId, UpdateUserDto updateDto)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                throw new ArgumentException("User not found");

            // Update fields if provided
            if (!string.IsNullOrEmpty(updateDto.Username))
                user.Username = updateDto.Username;
            if (!string.IsNullOrEmpty(updateDto.Email))
                user.Email = updateDto.Email;
            if (updateDto.IsVerified.HasValue)
                user.IsVerified = updateDto.IsVerified.Value;
            if (updateDto.IsActive.HasValue)
                user.IsActive = updateDto.IsActive.Value;
            if (updateDto.IsAdmin.HasValue)
                user.IsAdmin = updateDto.IsAdmin.Value;

            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);

            return new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> DeleteUserAsync(Guid userId)
    {
        try
        {
            // This would need to be implemented in the user repository
            // For now, just log and return true
            _logger.LogInformation("Deleting user: {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> ChangeUserPasswordAsync(Guid userId, string newPassword)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                throw new ArgumentException("User not found");

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing user password: {UserId}", userId);
            throw;
        }
    }

    // Competition Management
    public async Task<List<AdminCompetitionDto>> GetAllCompetitionsAsync()
    {
        try
        {
            // This would call the competition repository
            // For now, return mock data
            var mockCompetitions = new List<AdminCompetitionDto>
            {
                new AdminCompetitionDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Spring Championship 2024",
                    Description = "Annual spring tournament with exciting prizes",
                    StartDate = DateTime.UtcNow.AddDays(-15),
                    EndDate = DateTime.UtcNow.AddDays(15),
                    Status = "Active",
                    MaxTeams = 32,
                    CurrentTeams = 24,
                    EntryFee = 50,
                    PrizeFirst = 1000,
                    PrizeSecond = 500,
                    PrizeThird = 250,
                    TotalPrizePool = 1750,
                    Rules = "Standard Thunee rules apply",
                    IsPublic = true,
                    AllowSpectators = true,
                    MaxGamesPerTeam = 10,
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    UpdatedAt = DateTime.UtcNow.AddDays(-1)
                }
            };

            return mockCompetitions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all competitions");
            throw;
        }
    }

    public async Task<AdminCompetitionDto?> GetCompetitionByIdAsync(Guid competitionId)
    {
        try
        {
            // This would call the competition repository
            // For now, return mock data
            return new AdminCompetitionDto
            {
                Id = competitionId,
                Name = "Spring Championship 2024",
                Description = "Annual spring tournament with exciting prizes",
                StartDate = DateTime.UtcNow.AddDays(-15),
                EndDate = DateTime.UtcNow.AddDays(15),
                Status = "Active",
                MaxTeams = 32,
                CurrentTeams = 24,
                EntryFee = 50,
                PrizeFirst = 1000,
                PrizeSecond = 500,
                PrizeThird = 250,
                TotalPrizePool = 1750,
                Rules = "Standard Thunee rules apply",
                IsPublic = true,
                AllowSpectators = true,
                MaxGamesPerTeam = 10,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow.AddDays(-1)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition by ID: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<AdminCompetitionDto> CreateCompetitionAsync(CreateCompetitionDto createDto)
    {
        try
        {
            // This would create a new competition in the database
            // For now, return mock data
            var newCompetition = new AdminCompetitionDto
            {
                Id = Guid.NewGuid(),
                Name = createDto.Name,
                Description = createDto.Description,
                StartDate = createDto.StartDate,
                EndDate = createDto.EndDate,
                Status = "Upcoming",
                MaxTeams = createDto.MaxTeams,
                CurrentTeams = 0,
                EntryFee = createDto.EntryFee,
                PrizeFirst = createDto.PrizeFirst,
                PrizeSecond = createDto.PrizeSecond,
                PrizeThird = createDto.PrizeThird,
                TotalPrizePool = createDto.PrizeFirst + createDto.PrizeSecond + createDto.PrizeThird,
                Rules = createDto.Rules,
                IsPublic = createDto.IsPublic,
                AllowSpectators = createDto.AllowSpectators,
                MaxGamesPerTeam = createDto.MaxGamesPerTeam,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Created competition: {CompetitionName}", createDto.Name);
            return newCompetition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating competition: {CompetitionName}", createDto.Name);
            throw;
        }
    }

    public async Task<AdminCompetitionDto> UpdateCompetitionAsync(Guid competitionId, UpdateCompetitionDto updateDto)
    {
        try
        {
            // This would update the competition in the database
            // For now, return mock data
            var updatedCompetition = new AdminCompetitionDto
            {
                Id = competitionId,
                Name = updateDto.Name ?? "Updated Competition",
                Description = updateDto.Description ?? "Updated description",
                StartDate = updateDto.StartDate ?? DateTime.UtcNow,
                EndDate = updateDto.EndDate ?? DateTime.UtcNow.AddDays(30),
                Status = updateDto.Status ?? "Active",
                MaxTeams = updateDto.MaxTeams ?? 32,
                CurrentTeams = 15,
                EntryFee = updateDto.EntryFee ?? 50,
                PrizeFirst = updateDto.PrizeFirst ?? 1000,
                PrizeSecond = updateDto.PrizeSecond ?? 500,
                PrizeThird = updateDto.PrizeThird ?? 250,
                TotalPrizePool = (updateDto.PrizeFirst ?? 1000) + (updateDto.PrizeSecond ?? 500) + (updateDto.PrizeThird ?? 250),
                Rules = updateDto.Rules ?? "Standard rules",
                IsPublic = updateDto.IsPublic ?? true,
                AllowSpectators = updateDto.AllowSpectators ?? true,
                MaxGamesPerTeam = updateDto.MaxGamesPerTeam ?? 10,
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Updated competition: {CompetitionId}", competitionId);
            return updatedCompetition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating competition: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> DeleteCompetitionAsync(Guid competitionId)
    {
        try
        {
            // This would delete the competition from the database
            // For now, just log and return true
            _logger.LogInformation("Deleting competition: {CompetitionId}", competitionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition: {CompetitionId}", competitionId);
            throw;
        }
    }

    // Competition Teams Management
    public async Task<List<AdminCompetitionTeamDto>> GetCompetitionTeamsAsync(Guid competitionId)
    {
        try
        {
            // This would get teams from the database
            // For now, return mock data
            var mockTeams = new List<AdminCompetitionTeamDto>
            {
                new AdminCompetitionTeamDto
                {
                    Id = Guid.NewGuid(),
                    CompetitionId = competitionId,
                    TeamName = "Team Alpha",
                    Player1Id = Guid.NewGuid(),
                    Player1Username = "player1",
                    Player1Email = "<EMAIL>",
                    Player2Id = Guid.NewGuid(),
                    Player2Username = "player2",
                    Player2Email = "<EMAIL>",
                    InviteCode = "ABC123",
                    GamesPlayed = 5,
                    Points = 15,
                    BonusPoints = 2,
                    MaxGames = 10,
                    IsActive = true,
                    IsComplete = false,
                    RegisteredAt = DateTime.UtcNow.AddDays(-20),
                    CompletedAt = null
                }
            };

            return mockTeams;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition teams: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> DeleteCompetitionTeamAsync(Guid teamId)
    {
        try
        {
            // This would delete the team from the database
            // For now, just log and return true
            _logger.LogInformation("Deleting competition team: {TeamId}", teamId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition team: {TeamId}", teamId);
            throw;
        }
    }

    // Competition Games Management
    public async Task<List<AdminGameDto>> GetCompetitionGamesAsync(Guid competitionId)
    {
        try
        {
            // This would get games from the database
            // For now, return mock data
            var mockGames = new List<AdminGameDto>
            {
                new AdminGameDto
                {
                    Id = Guid.NewGuid(),
                    LobbyCode = "GAME123",
                    CompetitionId = competitionId,
                    CompetitionName = "Spring Championship 2024",
                    Team1Name = "Team Alpha",
                    Team2Name = "Team Beta",
                    Status = "Completed",
                    Team1Score = 12,
                    Team2Score = 8,
                    Team1BallsWon = 12,
                    Team2BallsWon = 8,
                    WinnerTeam = 1,
                    StartedAt = DateTime.UtcNow.AddHours(-2),
                    CompletedAt = DateTime.UtcNow.AddHours(-1),
                    CreatedAt = DateTime.UtcNow.AddHours(-3)
                }
            };

            return mockGames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition games: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<List<AdminGameDto>> GetAllGamesAsync()
    {
        try
        {
            // This would get all games from the database
            // For now, return mock data
            var mockGames = new List<AdminGameDto>
            {
                new AdminGameDto
                {
                    Id = Guid.NewGuid(),
                    LobbyCode = "GAME123",
                    CompetitionId = Guid.NewGuid(),
                    CompetitionName = "Spring Championship 2024",
                    Team1Name = "Team Alpha",
                    Team2Name = "Team Beta",
                    Status = "Completed",
                    Team1Score = 12,
                    Team2Score = 8,
                    Team1BallsWon = 12,
                    Team2BallsWon = 8,
                    WinnerTeam = 1,
                    StartedAt = DateTime.UtcNow.AddHours(-2),
                    CompletedAt = DateTime.UtcNow.AddHours(-1),
                    CreatedAt = DateTime.UtcNow.AddHours(-3)
                }
            };

            return mockGames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all games");
            throw;
        }
    }

    // Email Management
    public async Task<bool> SendCompetitionEmailAsync(Guid competitionId, CompetitionEmailDto emailDto)
    {
        try
        {
            // Get all players in the competition
            var teams = await GetCompetitionTeamsAsync(competitionId);
            var emails = new List<string>();

            foreach (var team in teams)
            {
                emails.Add(team.Player1Email);
                emails.Add(team.Player2Email);
            }

            // Remove duplicates
            emails = emails.Distinct().ToList();

            // Send emails to all players
            foreach (var email in emails)
            {
                await _emailService.SendEmailAsync(email, emailDto.Subject, emailDto.Message);
            }

            _logger.LogInformation("Sent competition email to {EmailCount} players in competition {CompetitionId}", 
                emails.Count, competitionId);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending competition email: {CompetitionId}", competitionId);
            throw;
        }
    }
}
