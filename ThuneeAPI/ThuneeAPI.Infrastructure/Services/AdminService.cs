using Microsoft.Extensions.Logging;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;

namespace ThuneeAPI.Infrastructure.Services;

public class AdminService : IAdminService
{
    private readonly IUserRepository _userRepository;
    private readonly ICompetitionRepository _competitionRepository;
    private readonly IEmailService _emailService;
    private readonly ILogger<AdminService> _logger;

    public AdminService(
        IUserRepository userRepository,
        ICompetitionRepository competitionRepository,
        IEmailService emailService,
        ILogger<AdminService> logger)
    {
        _userRepository = userRepository;
        _competitionRepository = competitionRepository;
        _emailService = emailService;
        _logger = logger;
    }

    // User Management
    public async Task<List<AdminUserDto>> GetAllUsersAsync()
    {
        try
        {
            var users = await _userRepository.GetAllAsync(1, 1000); // Get all users
            var adminUsers = users.Select(user => new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            }).ToList();

            return adminUsers;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all users");
            throw;
        }
    }

    public async Task<AdminUserDto?> GetUserByIdAsync(Guid userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null) return null;

            return new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by ID: {UserId}", userId);
            throw;
        }
    }

    public async Task<AdminUserDto> UpdateUserAsync(Guid userId, AdminUpdateUserDto updateDto)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                throw new ArgumentException("User not found");

            // Update fields if provided
            if (!string.IsNullOrEmpty(updateDto.Username))
                user.Username = updateDto.Username;
            if (!string.IsNullOrEmpty(updateDto.Email))
                user.Email = updateDto.Email;
            if (updateDto.IsVerified.HasValue)
                user.IsVerified = updateDto.IsVerified.Value;
            if (updateDto.IsActive.HasValue)
                user.IsActive = updateDto.IsActive.Value;
            if (updateDto.IsAdmin.HasValue)
                user.IsAdmin = updateDto.IsAdmin.Value;

            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);

            return new AdminUserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                IsVerified = user.IsVerified,
                IsActive = user.IsActive,
                IsAdmin = user.IsAdmin,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> DeleteUserAsync(Guid userId)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                return false;

            // For now, we'll mark the user as inactive instead of deleting
            // since there might be foreign key constraints
            user.IsActive = false;
            user.UpdatedAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("Deactivated user: {UserId}", userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user: {UserId}", userId);
            throw;
        }
    }

    public async Task<bool> ChangeUserPasswordAsync(Guid userId, string newPassword)
    {
        try
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
                throw new ArgumentException("User not found");

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userRepository.UpdateAsync(user);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing user password: {UserId}", userId);
            throw;
        }
    }

    // Competition Management
    public async Task<List<AdminCompetitionDto>> GetAllCompetitionsAsync()
    {
        try
        {
            var competitions = await _competitionRepository.GetAllAsync();
            var adminCompetitions = competitions.Select(comp => new AdminCompetitionDto
            {
                Id = comp.Id,
                Name = comp.Name,
                Description = comp.Description ?? "",
                StartDate = comp.StartDate,
                EndDate = comp.EndDate,
                Status = comp.Status,
                MaxTeams = comp.MaxTeams,
                CurrentTeams = comp.CurrentTeams,
                EntryFee = comp.EntryFee,
                PrizeFirst = decimal.TryParse(comp.PrizeFirst?.Replace("$", ""), out var first) ? first : 0,
                PrizeSecond = decimal.TryParse(comp.PrizeSecond?.Replace("$", ""), out var second) ? second : 0,
                PrizeThird = decimal.TryParse(comp.PrizeThird?.Replace("$", ""), out var third) ? third : 0,
                TotalPrizePool = comp.TotalPrizePool ?? 0,
                Rules = comp.Rules ?? "",
                IsPublic = comp.IsPublic,
                AllowSpectators = comp.AllowSpectators,
                MaxGamesPerTeam = comp.MaxGamesPerTeam,
                CreatedAt = comp.CreatedAt,
                UpdatedAt = comp.UpdatedAt
            }).ToList();

            return adminCompetitions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all competitions");
            throw;
        }
    }

    public async Task<AdminCompetitionDto?> GetCompetitionByIdAsync(Guid competitionId)
    {
        try
        {
            var competition = await _competitionRepository.GetByIdAsync(competitionId);
            if (competition == null) return null;

            return new AdminCompetitionDto
            {
                Id = competition.Id,
                Name = competition.Name,
                Description = competition.Description ?? "",
                StartDate = competition.StartDate,
                EndDate = competition.EndDate,
                Status = competition.Status,
                MaxTeams = competition.MaxTeams,
                CurrentTeams = competition.CurrentTeams,
                EntryFee = competition.EntryFee,
                PrizeFirst = decimal.TryParse(competition.PrizeFirst?.Replace("$", ""), out var first) ? first : 0,
                PrizeSecond = decimal.TryParse(competition.PrizeSecond?.Replace("$", ""), out var second) ? second : 0,
                PrizeThird = decimal.TryParse(competition.PrizeThird?.Replace("$", ""), out var third) ? third : 0,
                TotalPrizePool = competition.TotalPrizePool ?? 0,
                Rules = competition.Rules ?? "",
                IsPublic = competition.IsPublic,
                AllowSpectators = competition.AllowSpectators,
                MaxGamesPerTeam = competition.MaxGamesPerTeam,
                CreatedAt = competition.CreatedAt,
                UpdatedAt = competition.UpdatedAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition by ID: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<AdminCompetitionDto> CreateCompetitionAsync(CreateCompetitionDto createDto)
    {
        try
        {
            // Parse prize values from strings to decimals
            decimal.TryParse(createDto.PrizeFirst, out var prizeFirst);
            decimal.TryParse(createDto.PrizeSecond, out var prizeSecond);
            decimal.TryParse(createDto.PrizeThird, out var prizeThird);

            // Create the competition entity
            var competition = new Competition
            {
                Id = Guid.NewGuid(),
                Name = createDto.Name,
                Description = createDto.Description ?? "",
                StartDate = createDto.StartDate,
                EndDate = createDto.EndDate,
                Status = "Upcoming",
                MaxTeams = createDto.MaxTeams,
                CurrentTeams = 0,
                EntryFee = createDto.EntryFee,
                PrizeFirst = createDto.PrizeFirst, // Keep as string
                PrizeSecond = createDto.PrizeSecond, // Keep as string
                PrizeThird = createDto.PrizeThird, // Keep as string
                TotalPrizePool = prizeFirst + prizeSecond + prizeThird,
                Rules = createDto.Rules ?? "",
                IsPublic = createDto.IsPublic,
                AllowSpectators = createDto.AllowSpectators,
                MaxGamesPerTeam = 10, // Default value since not in DTO
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Save to database
            var createdCompetition = await _competitionRepository.CreateAsync(competition);

            // Return as AdminCompetitionDto
            var result = new AdminCompetitionDto
            {
                Id = createdCompetition.Id,
                Name = createdCompetition.Name,
                Description = createdCompetition.Description ?? "",
                StartDate = createdCompetition.StartDate,
                EndDate = createdCompetition.EndDate,
                Status = createdCompetition.Status,
                MaxTeams = createdCompetition.MaxTeams,
                CurrentTeams = createdCompetition.CurrentTeams,
                EntryFee = createdCompetition.EntryFee,
                PrizeFirst = decimal.TryParse(createdCompetition.PrizeFirst?.Replace("$", ""), out var first) ? first : 0,
                PrizeSecond = decimal.TryParse(createdCompetition.PrizeSecond?.Replace("$", ""), out var second) ? second : 0,
                PrizeThird = decimal.TryParse(createdCompetition.PrizeThird?.Replace("$", ""), out var third) ? third : 0,
                TotalPrizePool = createdCompetition.TotalPrizePool ?? 0,
                Rules = createdCompetition.Rules ?? "",
                IsPublic = createdCompetition.IsPublic,
                AllowSpectators = createdCompetition.AllowSpectators,
                MaxGamesPerTeam = createdCompetition.MaxGamesPerTeam,
                CreatedAt = createdCompetition.CreatedAt,
                UpdatedAt = createdCompetition.UpdatedAt
            };

            _logger.LogInformation("Created competition: {CompetitionName}", createDto.Name);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating competition: {CompetitionName}", createDto.Name);
            throw;
        }
    }

    public async Task<AdminCompetitionDto> UpdateCompetitionAsync(Guid competitionId, UpdateCompetitionDto updateDto)
    {
        try
        {
            // Parse prize values from strings to decimals
            decimal prizeFirst = 1000, prizeSecond = 500, prizeThird = 250;
            if (!string.IsNullOrEmpty(updateDto.PrizeFirst))
                decimal.TryParse(updateDto.PrizeFirst, out prizeFirst);
            if (!string.IsNullOrEmpty(updateDto.PrizeSecond))
                decimal.TryParse(updateDto.PrizeSecond, out prizeSecond);
            if (!string.IsNullOrEmpty(updateDto.PrizeThird))
                decimal.TryParse(updateDto.PrizeThird, out prizeThird);

            // This would update the competition in the database
            // For now, return mock data
            var updatedCompetition = new AdminCompetitionDto
            {
                Id = competitionId,
                Name = updateDto.Name ?? "Updated Competition",
                Description = updateDto.Description ?? "Updated description",
                StartDate = updateDto.StartDate ?? DateTime.UtcNow,
                EndDate = updateDto.EndDate ?? DateTime.UtcNow.AddDays(30),
                Status = "Active", // Default status since not in UpdateDto
                MaxTeams = updateDto.MaxTeams ?? 32,
                CurrentTeams = 15,
                EntryFee = updateDto.EntryFee ?? 50,
                PrizeFirst = prizeFirst,
                PrizeSecond = prizeSecond,
                PrizeThird = prizeThird,
                TotalPrizePool = prizeFirst + prizeSecond + prizeThird,
                Rules = updateDto.Rules ?? "Standard rules",
                IsPublic = updateDto.IsPublic ?? true,
                AllowSpectators = updateDto.AllowSpectators ?? true,
                MaxGamesPerTeam = 10, // Default value since not in DTO
                CreatedAt = DateTime.UtcNow.AddDays(-30),
                UpdatedAt = DateTime.UtcNow
            };

            _logger.LogInformation("Updated competition: {CompetitionId}", competitionId);
            return updatedCompetition;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating competition: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> DeleteCompetitionAsync(Guid competitionId)
    {
        try
        {
            await _competitionRepository.DeleteAsync(competitionId);
            _logger.LogInformation("Deleted competition: {CompetitionId}", competitionId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition: {CompetitionId}", competitionId);
            throw;
        }
    }

    // Competition Teams Management
    public async Task<List<AdminCompetitionTeamDto>> GetCompetitionTeamsAsync(Guid competitionId)
    {
        try
        {
            // This would get teams from the database
            // For now, return mock data
            var mockTeams = new List<AdminCompetitionTeamDto>
            {
                new AdminCompetitionTeamDto
                {
                    Id = Guid.NewGuid(),
                    CompetitionId = competitionId,
                    TeamName = "Team Alpha",
                    Player1Id = Guid.NewGuid(),
                    Player1Username = "player1",
                    Player1Email = "<EMAIL>",
                    Player2Id = Guid.NewGuid(),
                    Player2Username = "player2",
                    Player2Email = "<EMAIL>",
                    InviteCode = "ABC123",
                    GamesPlayed = 5,
                    Points = 15,
                    BonusPoints = 2,
                    MaxGames = 10,
                    IsActive = true,
                    IsComplete = false,
                    RegisteredAt = DateTime.UtcNow.AddDays(-20),
                    CompletedAt = null
                }
            };

            return mockTeams;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition teams: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<bool> DeleteCompetitionTeamAsync(Guid teamId)
    {
        try
        {
            // This would delete the team from the database
            // For now, just log and return true
            _logger.LogInformation("Deleting competition team: {TeamId}", teamId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition team: {TeamId}", teamId);
            throw;
        }
    }

    // Competition Games Management
    public async Task<List<AdminGameDto>> GetCompetitionGamesAsync(Guid competitionId)
    {
        try
        {
            // This would get games from the database
            // For now, return mock data
            var mockGames = new List<AdminGameDto>
            {
                new AdminGameDto
                {
                    Id = Guid.NewGuid(),
                    LobbyCode = "GAME123",
                    CompetitionId = competitionId,
                    CompetitionName = "Spring Championship 2024",
                    Team1Name = "Team Alpha",
                    Team2Name = "Team Beta",
                    Status = "Completed",
                    Team1Score = 12,
                    Team2Score = 8,
                    Team1BallsWon = 12,
                    Team2BallsWon = 8,
                    WinnerTeam = 1,
                    StartedAt = DateTime.UtcNow.AddHours(-2),
                    CompletedAt = DateTime.UtcNow.AddHours(-1),
                    CreatedAt = DateTime.UtcNow.AddHours(-3)
                }
            };

            return mockGames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition games: {CompetitionId}", competitionId);
            throw;
        }
    }

    public async Task<List<AdminGameDto>> GetAllGamesAsync()
    {
        try
        {
            // This would get all games from the database
            // For now, return mock data
            var mockGames = new List<AdminGameDto>
            {
                new AdminGameDto
                {
                    Id = Guid.NewGuid(),
                    LobbyCode = "GAME123",
                    CompetitionId = Guid.NewGuid(),
                    CompetitionName = "Spring Championship 2024",
                    Team1Name = "Team Alpha",
                    Team2Name = "Team Beta",
                    Status = "Completed",
                    Team1Score = 12,
                    Team2Score = 8,
                    Team1BallsWon = 12,
                    Team2BallsWon = 8,
                    WinnerTeam = 1,
                    StartedAt = DateTime.UtcNow.AddHours(-2),
                    CompletedAt = DateTime.UtcNow.AddHours(-1),
                    CreatedAt = DateTime.UtcNow.AddHours(-3)
                }
            };

            return mockGames;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all games");
            throw;
        }
    }

    // Email Management
    public async Task<bool> SendCompetitionEmailAsync(Guid competitionId, CompetitionEmailDto emailDto)
    {
        try
        {
            // Get all players in the competition
            var teams = await GetCompetitionTeamsAsync(competitionId);
            var emails = new List<string>();

            foreach (var team in teams)
            {
                emails.Add(team.Player1Email);
                emails.Add(team.Player2Email);
            }

            // Remove duplicates
            emails = emails.Distinct().ToList();

            // Send emails to all players
            foreach (var email in emails)
            {
                await _emailService.SendEmailAsync(email, emailDto.Subject, emailDto.Message);
            }

            _logger.LogInformation("Sent competition email to {EmailCount} players in competition {CompetitionId}", 
                emails.Count, competitionId);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending competition email: {CompetitionId}", competitionId);
            throw;
        }
    }
}
