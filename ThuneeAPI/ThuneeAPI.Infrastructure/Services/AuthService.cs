using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;
using ThuneeAPI.Core.Entities;
using BCrypt.Net;

namespace ThuneeAPI.Infrastructure.Services;

public class AuthService : IAuthService
{
    private readonly IUserRepository _userRepository;
    private readonly IConfiguration _configuration;
    private readonly IEmailService _emailService;

    public AuthService(IUserRepository userRepository, IConfiguration configuration, IEmailService emailService)
    {
        _userRepository = userRepository;
        _configuration = configuration;
        _emailService = emailService;
    }

    public async Task<AuthResponseDto> RegisterAsync(RegisterUserDto registerDto)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(registerDto.Username))
            throw new ArgumentException("Username is required");
        
        if (string.IsNullOrWhiteSpace(registerDto.Email))
            throw new ArgumentException("Email is required");
        
        if (string.IsNullOrWhiteSpace(registerDto.Password))
            throw new ArgumentException("Password is required");

        // Check if user already exists
        var existingUser = await _userRepository.GetByUsernameOrEmailAsync(registerDto.Username);
        var existingEmail = await _userRepository.GetByEmailAsync(registerDto.Email);

        if (existingUser != null)
        {
            throw new InvalidOperationException("Username already exists");
        }

        if (existingEmail != null)
        {
            throw new InvalidOperationException("Email already exists");
        }

        // Create new user
        var user = new User
        {
            Id = Guid.NewGuid(),
            Username = registerDto.Username,
            Email = registerDto.Email,
            PasswordHash = BCrypt.Net.BCrypt.HashPassword(registerDto.Password),
            IsVerified = true, // For now, auto-verify users
            IsActive = true,
            IsAdmin = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        user = await _userRepository.CreateAsync(user);

        // Send welcome email (don't await to avoid blocking registration)
        _ = Task.Run(async () =>
        {
            try
            {
                await _emailService.SendWelcomeEmailAsync(user.Email, user.Username);
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the registration
                Console.WriteLine($"Failed to send welcome email to {user.Email}: {ex.Message}");
            }
        });

        // Create response
        var userDto = new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            IsVerified = user.IsVerified,
            IsAdmin = user.IsAdmin,
            CreatedAt = user.CreatedAt
        };

        var token = GenerateJwtToken(userDto);
        var refreshToken = GenerateRefreshToken();

        return new AuthResponseDto
        {
            User = userDto,
            Token = token,
            RefreshToken = refreshToken
        };
    }

    public async Task<AuthResponseDto> LoginAsync(LoginUserDto loginDto)
    {
        // Validate input
        if (string.IsNullOrWhiteSpace(loginDto.Username))
            throw new ArgumentException("Username is required");
        
        if (string.IsNullOrWhiteSpace(loginDto.Password))
            throw new ArgumentException("Password is required");

        // Find user
        var user = await _userRepository.GetByUsernameOrEmailAsync(loginDto.Username);
        
        if (user == null || !BCrypt.Net.BCrypt.Verify(loginDto.Password, user.PasswordHash))
        {
            throw new UnauthorizedAccessException("Invalid username or password");
        }

        if (!user.IsActive)
        {
            throw new UnauthorizedAccessException("Account is deactivated");
        }

        // Update last login
        await _userRepository.UpdateLastLoginAsync(user.Id);

        // Create response
        var userDto = new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            IsVerified = user.IsVerified,
            LastLoginAt = user.LastLoginAt,
            CreatedAt = user.CreatedAt
        };

        var token = GenerateJwtToken(userDto);
        var refreshToken = GenerateRefreshToken();

        return new AuthResponseDto
        {
            User = userDto,
            Token = token,
            RefreshToken = refreshToken
        };
    }

    public async Task<UserDto> GetCurrentUserAsync(Guid userId)
    {
        var user = await _userRepository.GetByIdAsync(userId);

        if (user == null)
            throw new UnauthorizedAccessException("User not found");

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            IsVerified = user.IsVerified,
            LastLoginAt = user.LastLoginAt,
            CreatedAt = user.CreatedAt
        };
    }

    public async Task<UserDto?> GetUserByIdAsync(Guid userId)
    {
        var user = await _userRepository.GetByIdAsync(userId);

        if (user == null)
            return null;

        return new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            IsVerified = user.IsVerified,
            LastLoginAt = user.LastLoginAt,
            CreatedAt = user.CreatedAt
        };
    }

    public Task<bool> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["JwtSettings:SecretKey"]!);
            
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["JwtSettings:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["JwtSettings:Audience"],
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            return Task.FromResult(true);
        }
        catch
        {
            return Task.FromResult(false);
        }
    }

    public Task LogoutAsync(Guid userId)
    {
        // In a more complex implementation, you might invalidate refresh tokens here
        // For now, we'll just log the logout
        return Task.CompletedTask;
    }

    public string GenerateJwtToken(UserDto user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.UTF8.GetBytes(_configuration["JwtSettings:SecretKey"]!);
        
        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new(ClaimTypes.Name, user.Username),
            new(ClaimTypes.Email, user.Email),
            new("isVerified", user.IsVerified.ToString())
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(int.Parse(_configuration["JwtSettings:ExpiryInMinutes"]!)),
            Issuer = _configuration["JwtSettings:Issuer"],
            Audience = _configuration["JwtSettings:Audience"],
            SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public string GenerateRefreshToken()
    {
        var randomNumber = new byte[32];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomNumber);
        return Convert.ToBase64String(randomNumber);
    }
}
