using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public class EmailTestController : ControllerBase
{
    private readonly IEmailService _emailService;
    private readonly ILogger<EmailTestController> _logger;

    public EmailTestController(IEmailService emailService, ILogger<EmailTestController> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    /// <summary>
    /// Test email sending functionality (Development only)
    /// </summary>
    /// <param name="email">Email address to send test email to</param>
    /// <returns>Test result</returns>
    [HttpPost("send-test")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult> SendTestEmail([FromQuery] string email)
    {
        try
        {
            if (string.IsNullOrEmpty(email))
            {
                return BadRequest(new
                {
                    success = false,
                    error = "Email address is required"
                });
            }

            _logger.LogInformation("Sending test email to: {Email}", email);

            await _emailService.SendEmailAsync(email, "Test Email from Thunee API", 
                "<h1>Test Email</h1><p>This is a test email from the Thunee API email service.</p>");

            _logger.LogInformation("Test email sent successfully to: {Email}", email);

            return Ok(new
            {
                success = true,
                message = $"Test email sent successfully to {email}"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send test email to: {Email}", email);
            return StatusCode(500, new
            {
                success = false,
                error = $"Failed to send test email: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Test welcome email template
    /// </summary>
    /// <param name="email">Email address to send welcome email to</param>
    /// <param name="username">Username for the welcome email</param>
    /// <returns>Test result</returns>
    [HttpPost("send-welcome")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult> SendWelcomeEmail([FromQuery] string email, [FromQuery] string username)
    {
        try
        {
            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(username))
            {
                return BadRequest(new
                {
                    success = false,
                    error = "Email address and username are required"
                });
            }

            _logger.LogInformation("Sending welcome email to: {Email} for user: {Username}", email, username);

            await _emailService.SendWelcomeEmailAsync(email, username);

            _logger.LogInformation("Welcome email sent successfully to: {Email}", email);

            return Ok(new
            {
                success = true,
                message = $"Welcome email sent successfully to {email}"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send welcome email to: {Email}", email);
            return StatusCode(500, new
            {
                success = false,
                error = $"Failed to send welcome email: {ex.Message}"
            });
        }
    }

    /// <summary>
    /// Test team pairing email template
    /// </summary>
    /// <param name="email">Email address to send team pairing email to</param>
    /// <param name="username">Username for the email</param>
    /// <param name="teamName">Team name</param>
    /// <param name="partnerName">Partner's name</param>
    /// <returns>Test result</returns>
    [HttpPost("send-team-pairing")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<ActionResult> SendTeamPairingEmail(
        [FromQuery] string email, 
        [FromQuery] string username, 
        [FromQuery] string teamName, 
        [FromQuery] string partnerName)
    {
        try
        {
            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(username) || 
                string.IsNullOrEmpty(teamName) || string.IsNullOrEmpty(partnerName))
            {
                return BadRequest(new
                {
                    success = false,
                    error = "Email, username, team name, and partner name are all required"
                });
            }

            _logger.LogInformation("Sending team pairing email to: {Email} for user: {Username}", email, username);

            await _emailService.SendTeamPairingEmailAsync(email, username, teamName, partnerName);

            _logger.LogInformation("Team pairing email sent successfully to: {Email}", email);

            return Ok(new
            {
                success = true,
                message = $"Team pairing email sent successfully to {email}"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send team pairing email to: {Email}", email);
            return StatusCode(500, new
            {
                success = false,
                error = $"Failed to send team pairing email: {ex.Message}"
            });
        }
    }
}
