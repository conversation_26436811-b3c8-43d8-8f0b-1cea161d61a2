using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using ThuneeAPI.Application.DTOs;
using ThuneeAPI.Application.Interfaces;

namespace ThuneeAPI.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
public class AdminController : ControllerBase
{
    private readonly IAdminService _adminService;
    private readonly IAuthService _authService;
    private readonly ILogger<AdminController> _logger;

    public AdminController(
        IAdminService adminService,
        IAuthService authService,
        ILogger<AdminController> logger)
    {
        _adminService = adminService;
        _authService = authService;
        _logger = logger;
    }

    private async Task<bool> IsCurrentUserAdminAsync()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            return false;
        }

        var user = await _authService.GetUserByIdAsync(userId);
        return user?.IsAdmin == true;
    }

    private Guid GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out var userId))
        {
            throw new UnauthorizedAccessException("Invalid user token");
        }
        return userId;
    }

    #region User Management

    /// <summary>
    /// Get all users (Admin only)
    /// </summary>
    /// <returns>List of all users</returns>
    [HttpGet("users")]
    [ProducesResponseType(typeof(List<AdminUserDto>), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<ActionResult<List<AdminUserDto>>> GetAllUsers()
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var users = await _adminService.GetAllUsersAsync();
            
            return Ok(new
            {
                success = true,
                data = users,
                message = "Users retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all users");
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while retrieving users"
            });
        }
    }

    /// <summary>
    /// Get user by ID (Admin only)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>User details</returns>
    [HttpGet("users/{userId}")]
    [ProducesResponseType(typeof(AdminUserDto), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AdminUserDto>> GetUserById(Guid userId)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var user = await _adminService.GetUserByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new
                {
                    success = false,
                    error = "User not found"
                });
            }

            return Ok(new
            {
                success = true,
                data = user,
                message = "User retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user by ID: {UserId}", userId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while retrieving user"
            });
        }
    }

    /// <summary>
    /// Update user (Admin only)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="updateDto">Update data</param>
    /// <returns>Updated user</returns>
    [HttpPost("users/{userId}")]
    [ProducesResponseType(typeof(AdminUserDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AdminUserDto>> UpdateUser(Guid userId, [FromBody] AdminUpdateUserDto updateDto)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var updatedUser = await _adminService.UpdateUserAsync(userId, updateDto);
            
            return Ok(new
            {
                success = true,
                data = updatedUser,
                message = "User updated successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating user: {UserId}", userId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while updating user"
            });
        }
    }

    /// <summary>
    /// Delete user (Admin only)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("users/{userId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteUser(Guid userId)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            // Prevent admin from deleting themselves
            var currentUserId = GetCurrentUserId();
            if (userId == currentUserId)
            {
                return BadRequest(new
                {
                    success = false,
                    error = "You cannot delete your own account"
                });
            }

            var result = await _adminService.DeleteUserAsync(userId);
            if (!result)
            {
                return NotFound(new
                {
                    success = false,
                    error = "User not found"
                });
            }

            return Ok(new
            {
                success = true,
                message = "User deleted successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting user: {UserId}", userId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while deleting user"
            });
        }
    }

    /// <summary>
    /// Change user password (Admin only)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="passwordDto">New password data</param>
    /// <returns>Success response</returns>
    [HttpPost("users/{userId}/password")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> ChangeUserPassword(Guid userId, [FromBody] ChangeUserPasswordDto passwordDto)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            if (string.IsNullOrEmpty(passwordDto.NewPassword) || passwordDto.NewPassword.Length < 6)
            {
                return BadRequest(new
                {
                    success = false,
                    error = "Password must be at least 6 characters long"
                });
            }

            var result = await _adminService.ChangeUserPasswordAsync(userId, passwordDto.NewPassword);
            if (!result)
            {
                return NotFound(new
                {
                    success = false,
                    error = "User not found"
                });
            }

            return Ok(new
            {
                success = true,
                message = "Password changed successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing user password: {UserId}", userId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while changing password"
            });
        }
    }

    #endregion

    #region Competition Management

    /// <summary>
    /// Get all competitions (Admin only)
    /// </summary>
    /// <returns>List of all competitions</returns>
    [HttpGet("competitions")]
    [ProducesResponseType(typeof(List<AdminCompetitionDto>), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<ActionResult<List<AdminCompetitionDto>>> GetAllCompetitions()
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var competitions = await _adminService.GetAllCompetitionsAsync();
            
            return Ok(new
            {
                success = true,
                data = competitions,
                message = "Competitions retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting all competitions");
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while retrieving competitions"
            });
        }
    }

    /// <summary>
    /// Create competition (Admin only)
    /// </summary>
    /// <param name="createDto">Competition creation data</param>
    /// <returns>Created competition</returns>
    [HttpPost("competitions")]
    [ProducesResponseType(typeof(AdminCompetitionDto), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<ActionResult<AdminCompetitionDto>> CreateCompetition([FromBody] CreateCompetitionDto createDto)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var competition = await _adminService.CreateCompetitionAsync(createDto);
            
            return CreatedAtAction(
                nameof(GetCompetitionById),
                new { competitionId = competition.Id },
                new
                {
                    success = true,
                    data = competition,
                    message = "Competition created successfully"
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating competition");
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while creating competition"
            });
        }
    }

    /// <summary>
    /// Get competition by ID (Admin only)
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>Competition details</returns>
    [HttpGet("competitions/{competitionId}")]
    [ProducesResponseType(typeof(AdminCompetitionDto), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AdminCompetitionDto>> GetCompetitionById(Guid competitionId)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var competition = await _adminService.GetCompetitionByIdAsync(competitionId);
            if (competition == null)
            {
                return NotFound(new
                {
                    success = false,
                    error = "Competition not found"
                });
            }

            return Ok(new
            {
                success = true,
                data = competition,
                message = "Competition retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition by ID: {CompetitionId}", competitionId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while retrieving competition"
            });
        }
    }

    /// <summary>
    /// Update competition (Admin only)
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <param name="updateDto">Update data</param>
    /// <returns>Updated competition</returns>
    [HttpPost("competitions/{competitionId}")]
    [ProducesResponseType(typeof(AdminCompetitionDto), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult<AdminCompetitionDto>> UpdateCompetition(Guid competitionId, [FromBody] UpdateCompetitionDto updateDto)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var updatedCompetition = await _adminService.UpdateCompetitionAsync(competitionId, updateDto);

            return Ok(new
            {
                success = true,
                data = updatedCompetition,
                message = "Competition updated successfully"
            });
        }
        catch (ArgumentException ex)
        {
            return NotFound(new
            {
                success = false,
                error = ex.Message
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating competition: {CompetitionId}", competitionId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while updating competition"
            });
        }
    }

    /// <summary>
    /// Delete competition (Admin only)
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("competitions/{competitionId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteCompetition(Guid competitionId)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var result = await _adminService.DeleteCompetitionAsync(competitionId);
            if (!result)
            {
                return NotFound(new
                {
                    success = false,
                    error = "Competition not found"
                });
            }

            return Ok(new
            {
                success = true,
                message = "Competition deleted successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition: {CompetitionId}", competitionId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while deleting competition"
            });
        }
    }

    /// <summary>
    /// Get competition teams (Admin only)
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>List of teams in competition</returns>
    [HttpGet("competitions/{competitionId}/teams")]
    [ProducesResponseType(typeof(List<AdminCompetitionTeamDto>), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<ActionResult<List<AdminCompetitionTeamDto>>> GetCompetitionTeams(Guid competitionId)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var teams = await _adminService.GetCompetitionTeamsAsync(competitionId);

            return Ok(new
            {
                success = true,
                data = teams,
                message = "Competition teams retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition teams: {CompetitionId}", competitionId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while retrieving competition teams"
            });
        }
    }

    /// <summary>
    /// Delete competition team (Admin only)
    /// </summary>
    /// <param name="teamId">Team ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("teams/{teamId}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    [ProducesResponseType(404)]
    public async Task<ActionResult> DeleteCompetitionTeam(Guid teamId)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var result = await _adminService.DeleteCompetitionTeamAsync(teamId);
            if (!result)
            {
                return NotFound(new
                {
                    success = false,
                    error = "Team not found"
                });
            }

            return Ok(new
            {
                success = true,
                message = "Team deleted successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting competition team: {TeamId}", teamId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while deleting team"
            });
        }
    }

    /// <summary>
    /// Get competition games (Admin only)
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <returns>List of games in competition</returns>
    [HttpGet("competitions/{competitionId}/games")]
    [ProducesResponseType(typeof(List<AdminGameDto>), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<ActionResult<List<AdminGameDto>>> GetCompetitionGames(Guid competitionId)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            var games = await _adminService.GetCompetitionGamesAsync(competitionId);

            return Ok(new
            {
                success = true,
                data = games,
                message = "Competition games retrieved successfully"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting competition games: {CompetitionId}", competitionId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while retrieving competition games"
            });
        }
    }

    /// <summary>
    /// Send email to all players in competition (Admin only)
    /// </summary>
    /// <param name="competitionId">Competition ID</param>
    /// <param name="emailDto">Email data</param>
    /// <returns>Success response</returns>
    [HttpPost("competitions/{competitionId}/email")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<ActionResult> SendCompetitionEmail(Guid competitionId, [FromBody] CompetitionEmailDto emailDto)
    {
        try
        {
            if (!await IsCurrentUserAdminAsync())
            {
                return Forbid();
            }

            if (string.IsNullOrEmpty(emailDto.Subject) || string.IsNullOrEmpty(emailDto.Message))
            {
                return BadRequest(new
                {
                    success = false,
                    error = "Subject and message are required"
                });
            }

            var result = await _adminService.SendCompetitionEmailAsync(competitionId, emailDto);
            if (!result)
            {
                return BadRequest(new
                {
                    success = false,
                    error = "Failed to send emails"
                });
            }

            return Ok(new
            {
                success = true,
                message = "Emails sent successfully to all competition players"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending competition email: {CompetitionId}", competitionId);
            return StatusCode(500, new
            {
                success = false,
                error = "An error occurred while sending emails"
            });
        }
    }

    #endregion
}
