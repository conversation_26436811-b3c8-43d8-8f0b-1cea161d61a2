{"ConnectionStrings": {"DefaultConnection": "Server=192.168.20.121; Database=GoldRushThunee; User Id=EG-Dev; Password=Password01?; TrustServerCertificate=True;"}, "JwtSettings": {"SecretKey": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "ThuneeAPI", "Audience": "ThuneeClient", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 30}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}, "File": {"Path": "logs/thunee-api-.txt", "LogLevel": {"Default": "Information"}}}, "AllowedHosts": "*", "Cors": {"AllowedOrigins": ["http://**************:96", "http://**************:3001", "http://localhost:96", "http://localhost:5173", "http://localhost:3000"]}, "ApiSettings": {"BaseUrl": "http://**************:8080", "Environment": "Production"}}